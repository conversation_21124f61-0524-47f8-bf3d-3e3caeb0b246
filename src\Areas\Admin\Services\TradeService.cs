using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;
using RazeWinComTr.Areas.Admin.ViewModels.ReferralReward;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

public class TradeService : ITradeService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public TradeService(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
    }

    public async Task<Trade?> GetByIdAsync(int id)
    {
        return await _context.Trades
            .Include(t => t.User)
            .Include(t => t.Coin)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.ReferredUser)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.Package)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<List<TradeViewModel>> GetListAsync()
    {
        return await _context.Trades
            .Include(t => t.User)
            .Include(t => t.Coin)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.ReferredUser)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.Package)
            .Select(t => new TradeViewModel
            {
                Id = t.Id,
                UserId = t.UserId,
                UserEmail = t.User == null ? string.Empty : t.User.Email,
                CoinId = t.CoinId,
                CoinCode = t.Coin == null ? string.Empty : t.Coin.PairCode,
                Type = t.Type,
                CoinAmount = t.CoinAmount,
                CoinRate = t.CoinRate,
                TryAmount = t.TryAmount,
                PreviousBalance = t.PreviousBalance,
                NewBalance = t.NewBalance,
                PreviousCoinBalance = t.PreviousCoinBalance,
                NewCoinBalance = t.NewCoinBalance,
                PreviousWalletBalance = t.PreviousWalletBalance,
                NewWalletBalance = t.NewWalletBalance,
                CreatedDate = t.CreatedDate,
                IsActive = t.IsActive,
                ReferralRewardId = t.ReferralRewardId,
                ReferralReward = t.ReferralReward == null ? null : ReferralRewardDetailsViewModel.FromEntity(t.ReferralReward)
            })
            .OrderByDescending(t => t.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<TradeViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.Trades
            .Include(t => t.Coin)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.ReferredUser)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.Package)
            .Where(t => t.UserId == userId && t.IsActive)
            .Select(t => new TradeViewModel
            {
                Id = t.Id,
                UserId = t.UserId,
                UserEmail = t.User == null ? string.Empty : t.User.Email,
                CoinId = t.CoinId,
                CoinCode = t.Coin == null ? string.Empty : t.Coin.PairCode,
                Type = t.Type,
                CoinAmount = t.CoinAmount,
                CoinRate = t.CoinRate,
                TryAmount = t.TryAmount,
                PreviousBalance = t.PreviousBalance,
                NewBalance = t.NewBalance,
                PreviousCoinBalance = t.PreviousCoinBalance,
                NewCoinBalance = t.NewCoinBalance,
                PreviousWalletBalance = t.PreviousWalletBalance,
                NewWalletBalance = t.NewWalletBalance,
                CreatedDate = t.CreatedDate,
                IsActive = t.IsActive,
                ReferralRewardId = t.ReferralRewardId,
                ReferralReward = t.ReferralReward == null ? null : ReferralRewardDetailsViewModel.FromEntity(t.ReferralReward)
            })
            .OrderByDescending(t => t.CreatedDate)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Trades.FindAsync(id);
        if (entity != null)
        {
            _context.Trades.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public virtual async Task<Trade> CreateAsync(Trade trade)
    {
        _context.Trades.Add(trade);
        await _context.SaveChangesAsync();
        return trade;
    }

    public async Task UpdateAsync(Trade trade)
    {
        _context.Entry(trade).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }
}
