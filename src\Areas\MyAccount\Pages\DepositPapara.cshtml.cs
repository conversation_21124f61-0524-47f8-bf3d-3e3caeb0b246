using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class DepositPaparaModel : PageModel
    {
        private readonly SettingService _settingService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly AppDbContext _context;
        private readonly DepositService _depositService;

        public DepositPaparaModel(
            SettingService settingService,
            IStringLocalizer<SharedResource> localizer,
            AppDbContext context,
            DepositService depositService)
        {
            _settingService = settingService;
            _localizer = localizer;
            _context = context;
            _depositService = depositService;
        }

        [BindProperty]
        public PaparaDepositViewModel DepositInfo { get; set; } = new();

        public string? AlertMessage { get; set; }
        public string? AlertType { get; set; }
        public int? CurrentUserId { get; set; }
        public string? UserFullName { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            CurrentUserId = userId.Value;

            // Get user information
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            UserFullName = $"{user.Name} {user.Surname}";

            // Pre-fill the form with user data
            DepositInfo.FullName = UserFullName;
            DepositInfo.Phone = user.PhoneNumber ?? string.Empty;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            CurrentUserId = userId.Value;

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Get user information
                var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    return NotFound();
                }

                // Get the combined amount from the integer and fractional parts
                decimal amount = DepositInfo.GetCombinedAmount();

                // Validate Amount
                if (amount <= 0)
                {
                    ModelState.AddModelError("DepositInfo.AmountInteger", _localizer["Please enter a valid amount"]);
                    return Page();
                }

                // Create deposit record
                var extraData = new PaparaExtraData
                {
                    PaparaNumber = DepositInfo.PaparaNumber,
                    Phone = DepositInfo.Phone
                };

                var deposit = new Deposit
                {
                    UserId = userId.Value,
                    DepositType = "Papara",
                    Amount = amount,
                    FullName = DepositInfo.FullName,
                    ExtraData = JsonSerializer.Serialize(extraData),
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                    CreatedDate = DateTime.UtcNow,
                    ProcessStatus = "Submitted",
                    Status = DepositStatus.Pending
                };

                // Use the deposit service to create the deposit
                // This ensures it's only added to balance history when approved
                await _depositService.CreateAsync(deposit);

                // Set success message to be displayed with SweetAlert
                AlertMessage = _localizer["Your deposit request has been received. It will be processed as soon as possible."].Value;
                AlertType = "success";

                return Page();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, ex.Message);
                return Page();
            }
        }
    }

    public class PaparaDepositViewModel
    {
        [Required(ErrorMessage = "Full Name Required")]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Papara Number Required")]
        [Display(Name = "Papara Number")]
        public string PaparaNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone Number Required")]
        [Display(Name = "Phone Number")]
        public string Phone { get; set; } = string.Empty;

        // Integer part of the amount
        [Required(ErrorMessage = "Amount Required")]
        [Display(Name = "Amount")]
        [Range(0, int.MaxValue, ErrorMessage = "Please Enter Valid Amount")]
        public int AmountInteger { get; set; }

        // Fractional part of the amount (as string to preserve leading zeros)
        [Display(Name = "Amount (Cents)")]
        public string AmountFraction { get; set; } = "00";

        /// <summary>
        /// Gets the combined decimal amount from the integer and fractional parts
        /// </summary>
        /// <returns>The combined decimal amount in invariant culture</returns>
        public decimal GetCombinedAmount()
        {
            // Combine the integer and fractional parts
            string combinedString = $"{AmountInteger}.{AmountFraction}";

            // Parse using invariant culture
            if (decimal.TryParse(combinedString, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            // Fallback to just the integer part if parsing fails
            return AmountInteger;
        }
    }

    public class PaparaExtraData
    {
        public string PaparaNumber { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
    }
}
