using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Withdrawal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class WithdrawalModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly IWalletService _walletService;
    private readonly WithdrawalService _withdrawalService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public WithdrawalModel(AppDbContext context, IWalletService walletService, WithdrawalService withdrawalService, IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _walletService = walletService;
        _withdrawalService = withdrawalService;
        _localizer = localizer;
        Withdrawals = new List<WithdrawalViewModel>();
        WithdrawalInput = new WithdrawalInputModel();
    }

    public decimal CurrentBalance { get; set; }
    public decimal AvailableWithdrawalLimit { get; set; }
    public List<WithdrawalViewModel> Withdrawals { get; set; }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }

    [BindProperty]
    public WithdrawalInputModel WithdrawalInput { get; set; }

    [TempData]
    public string SuccessMessage { get; set; } = string.Empty;

    [TempData]
    public string ErrorMessage { get; set; } = string.Empty;

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user's TRY balance from User table
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }
        CurrentBalance = user.Balance;

        // Get pending withdrawal requests for this user
        var pendingWithdrawals = await _context.Withdrawals
            .Where(w => w.UserId == userId && w.Status == WithdrawalStatus.Pending)
            .ToListAsync();

        // Calculate total pending withdrawal amount
        decimal totalPendingAmount = pendingWithdrawals.Sum(w => w.WithdrawalAmount);

        // Calculate available withdrawal limit (balance minus pending withdrawals)
        AvailableWithdrawalLimit = user.Balance - totalPendingAmount;

        // Get user's withdrawal history
        Withdrawals = await _withdrawalService.GetByUserIdAsync(userId.Value);

        // Set user information
        UserFullName = $"{user.Name} {user.Surname}";
        UserEmail = user.Email;
        UserPhone = user.PhoneNumber;
        UserCreatedDate = user.CrDate;

        // Pre-fill withdrawal form with user data
        WithdrawalInput.AccountHolder = $"{user.Name} {user.Surname}";

        // Pre-fill IBAN if available
        if (!string.IsNullOrEmpty(user.Iban))
        {
            WithdrawalInput.Iban = user.Iban;
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Manual server-side validation with localized messages

        // Get the combined amount from the integer and fractional parts
        decimal amount = WithdrawalInput.GetCombinedAmount();

        // Validate Amount
        if (amount <= 0)
        {
            ModelState.AddModelError("WithdrawalInput.AmountInteger", _localizer["Please enter a valid amount"]);
        }

        // Validate AccountHolder
        if (string.IsNullOrWhiteSpace(WithdrawalInput.AccountHolder))
        {
            ModelState.AddModelError("WithdrawalInput.AccountHolder", _localizer["Please enter account holder name"]);
        }
        else if (WithdrawalInput.AccountHolder.Length > 100)
        {
            ModelState.AddModelError("WithdrawalInput.AccountHolder", _localizer["Account holder name cannot exceed 100 characters"]);
        }

        // Validate IBAN
        if (string.IsNullOrWhiteSpace(WithdrawalInput.Iban))
        {
            ModelState.AddModelError("WithdrawalInput.Iban", _localizer["Please enter IBAN"]);
        }
        else if (WithdrawalInput.Iban.Length > 50)
        {
            ModelState.AddModelError("WithdrawalInput.Iban", _localizer["IBAN cannot exceed 50 characters"]);
        }
        else if (!System.Text.RegularExpressions.Regex.IsMatch(WithdrawalInput.Iban, @"^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$"))
        {
            ModelState.AddModelError("WithdrawalInput.Iban", _localizer["Please enter a valid IBAN"]);
        }

        if (!ModelState.IsValid)
        {
            await OnGetAsync();
            return Page();
        }

        try
        {
            // Get user's TRY balance from User table
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            // Get pending withdrawal requests for this user
            var pendingWithdrawals = await _context.Withdrawals
                .Where(w => w.UserId == userId && w.Status == WithdrawalStatus.Pending)
                .ToListAsync();

            // Calculate total pending withdrawal amount
            decimal totalPendingAmount = pendingWithdrawals.Sum(w => w.WithdrawalAmount);

            // Calculate available withdrawal limit (balance minus pending withdrawals)
            decimal availableLimit = user.Balance - totalPendingAmount;

            // Check if user has sufficient balance for this new withdrawal
            if (availableLimit < amount)
            {
                string errorMessage = _localizer["Insufficient Balance For Withdrawal",
                    NumberFormatHelper.FormatDecimalWithThousandSeparator(availableLimit),
                    _localizer["Currency_Symbol"].Value].Value;

                ModelState.AddModelError("WithdrawalInput.AmountInteger", errorMessage);
                await OnGetAsync();
                return Page();
            }

            // Create withdrawal record
            var withdrawal = new Withdrawal
            {
                UserId = userId.Value,
                FullName = $"{user.Name} {user.Surname}",
                Email = user.Email,
                Balance = user.Balance,
                WithdrawalAmount = amount,
                AccountHolder = WithdrawalInput.AccountHolder,
                Iban = WithdrawalInput.Iban,
                Status = WithdrawalStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            await _withdrawalService.CreateAsync(withdrawal);

            // Set success message for SweetAlert
            SuccessMessage = _localizer["Withdrawal Request Submitted"];
            return RedirectToPage();
        }
        catch (Exception ex)
        {
            ErrorMessage = _localizer["Withdrawal Submission Error", ex.Message].Value;
            await OnGetAsync();
            return Page();
        }
    }

    public class WithdrawalInputModel
    {
        // Integer part of the amount
        public int AmountInteger { get; set; }

        // Fractional part of the amount (as string to preserve leading zeros)
        public string AmountFraction { get; set; } = "00";

        public string AccountHolder { get; set; } = string.Empty;

        public string Iban { get; set; } = string.Empty;

        /// <summary>
        /// Gets the combined decimal amount from the integer and fractional parts
        /// </summary>
        /// <returns>The combined decimal amount in invariant culture</returns>
        public decimal GetCombinedAmount()
        {
            // Combine the integer and fractional parts
            string combinedString = $"{AmountInteger}.{AmountFraction}";

            // Parse using invariant culture
            if (decimal.TryParse(combinedString, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            // Fallback to just the integer part if parsing fails
            return AmountInteger;
        }
    }
}
