using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.ViewModels.Wallet
{
    public class WalletViewModel
    {
        public int Id { get; set; }

        public int UserId { get; set; }

        public string UserEmail { get; set; } = string.Empty;

        public int CoinId { get; set; }

        public string CoinName { get; set; } = string.Empty;

        public string CoinCode { get; set; } = string.Empty;

        [DisplayFormat(DataFormatString = "{0:N8}")]
        public decimal Balance { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        // Coin price for TRY value calculation
        public decimal CoinPrice { get; set; }
    }
}
