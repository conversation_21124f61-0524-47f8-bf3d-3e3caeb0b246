using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.ViewComponents
{
    public class UserBalanceViewComponent : ViewComponent
    {
        private readonly AppDbContext _context;
        private readonly HybridCache _cache;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public UserBalanceViewComponent(
            AppDbContext context,
            HybridCache cache,
            IStringLocalizer<SharedResource> localizer)
        {
            _context = context;
            _cache = cache;
            _localizer = localizer;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var userId = HttpContext.User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return View(new UserBalanceViewModel { TryBalance = 0 });
            }

            // Cache key based on user ID
            string cacheKey = $"UserTryBalance_{userId.Value}";

            // Try to get from cache first
            var cachedBalance = await _cache.GetOrCreateAsync<decimal>(
                cacheKey,
                async (cancel) =>
                {
                    // Get balance directly from User table
                    var user = await _context.Users.FindAsync(userId.Value);
                    return user?.Balance ?? 0;
                },
                new HybridCacheEntryOptions
                {
                    Expiration = TimeSpan.FromSeconds(30)
                });

            return View(new UserBalanceViewModel
            {
                TryBalance = cachedBalance,
                BalanceText = _localizer["TRY Balance"]
            });
        }
    }

    public class UserBalanceViewModel
    {
        public decimal TryBalance { get; set; }
        public string BalanceText { get; set; } = string.Empty;
    }
}
