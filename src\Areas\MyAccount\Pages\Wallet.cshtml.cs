using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class WalletModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly IWalletService _walletService;
    private readonly ITradeService _tradeService;

    public WalletModel(AppDbContext context, IWalletService walletService, ITradeService tradeService)
    {
        _context = context;
        _walletService = walletService;
        _tradeService = tradeService;
        Wallets = new List<WalletViewModel>();
        Trades = new List<TradeViewModel>();
    }

    public List<WalletViewModel> Wallets { get; set; }
    public List<TradeViewModel> Trades { get; set; }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user information
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        UserFullName = $"{user.Name} {user.Surname}";
        UserEmail = user.Email;
        UserPhone = user.PhoneNumber;
        UserCreatedDate = user.CrDate;

        // Get wallet balances
        var wallets = await _walletService.GetByUserIdAsync(userId.Value);
        Wallets = wallets
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = user.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin.PairCode,
                Balance = w.Balance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate
            })
            .OrderByDescending(w => w.Balance)
            .ToList();

        // Get trade history
        Trades = await _tradeService.GetByUserIdAsync(userId.Value);

        return Page();
    }
}
