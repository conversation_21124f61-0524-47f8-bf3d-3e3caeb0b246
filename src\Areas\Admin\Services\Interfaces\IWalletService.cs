using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

public interface IWalletService
{
    Task<Wallet?> GetByIdAsync(int id);
    Task<List<Wallet>> GetByUserIdAsync(int userId);
    Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN);
    Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId, AppDbContext? existingContext = null);
    Task<List<WalletViewModel>> GetListAsync();
    Task<Wallet> CreateAsync(Wallet wallet, AppDbContext? existingContext = null);
    Task UpdateAsync(Wallet wallet, AppDbContext? existingContext = null);
    Task DeleteAsync(int id);

    // NEW METHODS - Available Balance
    Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId);
    Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext? existingContext = null);
    Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null);

}
