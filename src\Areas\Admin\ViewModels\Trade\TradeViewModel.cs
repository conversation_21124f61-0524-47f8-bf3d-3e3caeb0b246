using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.ReferralReward;

namespace RazeWinComTr.Areas.Admin.ViewModels.Trade
{
    public class TradeViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public int CoinId { get; set; }
        public string CoinCode { get; set; } = string.Empty;
        public TradeType Type { get; set; }
        public decimal CoinAmount { get; set; }
        public decimal CoinRate { get; set; }
        public decimal TryAmount { get; set; }
        public decimal PreviousBalance { get; set; }
        public decimal NewBalance { get; set; }
        public decimal PreviousCoinBalance { get; set; }
        public decimal NewCoinBalance { get; set; }
        public decimal PreviousWalletBalance { get; set; }
        public decimal NewWalletBalance { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
        public int? ReferralRewardId { get; set; }

        // Referral reward details
        public ReferralRewardDetailsViewModel? ReferralReward { get; set; }

        // Helper property to get formatted referral reward description
        public string? GetReferralRewardDescription() => ReferralReward?.GetReferralRewardDescription();
    }
}
