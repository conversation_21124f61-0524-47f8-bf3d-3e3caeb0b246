﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace RazeWinComTr.Areas.Admin.DbModel;

public partial class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options)
        : base(options)
    {
    }
    public virtual DbSet<Trade> Trades { get; set; }
    public virtual DbSet<Deposit> Deposits { get; set; }
    public virtual DbSet<Withdrawal> Withdrawals { get; set; }
    public virtual DbSet<Bank> Banks { get; set; }
    public virtual DbSet<Setting> Settings { get; set; }
    public virtual DbSet<Wallet> Wallets { get; set; }
    public virtual DbSet<Market> Markets { get; set; }
    public virtual DbSet<Role> Roles { get; set; }
    public virtual DbSet<User> Users { get; set; }
    public virtual DbSet<UserRoleRelation> UserRoleRelations { get; set; }
    public virtual DbSet<BalanceTransaction> BalanceTransactions { get; set; }

    // Referral system entities
    public virtual DbSet<Package> Packages { get; set; }
    public virtual DbSet<UserPackage> UserPackages { get; set; }
    public virtual DbSet<ReferralReward> ReferralRewards { get; set; }
    public virtual DbSet<PackageRewardPercentage> PackageRewardPercentages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Role>(entity =>
        {
            entity.Property(e => e.RoleId).ValueGeneratedNever();
        });

        modelBuilder.Entity<UserRoleRelation>(entity =>
        {
            entity.HasOne(d => d.Role).WithMany(p => p.UserRoleRelations).OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(d => d.User).WithMany(p => p.UserRoleRelations).OnDelete(DeleteBehavior.Restrict);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}