using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.ViewModels.TransactionReversal;

namespace RazeWinComTr.Areas.Admin.Services;

public class TransactionReversalService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly DepositService _depositService;

    public TransactionReversalService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer,
        DepositService depositService)
    {
        _context = context;
        _localizer = localizer;
        _depositService = depositService;
    }

    public async Task<DepositReversalEligibilityResult> CheckDepositReversalEligibilityAsync(int depositId)
    {
        var result = new DepositReversalEligibilityResult
        {
            CanReverse = false,
            Message = string.Empty,
            Checks = new List<ReversalCheckItem>()
        };

        // Deposit kaydını al
        var deposit = await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == depositId);

        if (deposit == null)
        {
            result.Message = _localizer["Deposit not found"].Value;
            return result;
        }

        // 1. Deposit durumu kontrolü
        var statusCheck = new ReversalCheckItem
        {
            Description = _localizer["Deposit Status Check"].Value,
            Passed = deposit.Status == DepositStatus.Approved
        };

        if (!statusCheck.Passed)
        {
            statusCheck.Details = _localizer["Deposit must be in Approved state"].Value;
        }

        result.Checks.Add(statusCheck);

        if (!statusCheck.Passed)
        {
            result.Message = _localizer["Deposit is not in approved state"].Value;
            return result;
        }

        var userId = deposit.UserId;

        // 2. Ödül dağıtımı kontrolü
        var rewardCheck = new ReversalCheckItem
        {
            Description = _localizer["Reward Distribution Check"].Value,
            Passed = deposit.RewardStatus != DepositRewardStatus.Distributed
        };

        if (!rewardCheck.Passed)
        {
            var rewardCount = await _context.ReferralRewards.CountAsync(r => r.DepositId == depositId);
            rewardCheck.Details = _localizer["Rewards have been distributed to {0} users", rewardCount].Value;
        }
        else
        {
            rewardCheck.Details = _localizer["No rewards have been distributed yet"].Value;
        }

        result.Checks.Add(rewardCheck);

        if (!rewardCheck.Passed)
        {
            result.Message = _localizer["Cannot reverse deposit because rewards have been distributed"].Value;
            return result;
        }

        // 3. Para yatırma işleminin onaylandığı tarihi bul
        var transactionCheck = new ReversalCheckItem
        {
            Description = _localizer["Deposit Approval Transaction Check"].Value,
            Passed = false
        };

        var depositApprovalTransaction = await _context.BalanceTransactions
            .Where(t => t.UserId == userId &&
                       t.ReferenceId == depositId &&
                       t.ReferenceType == TransactionType.Deposit &&
                       t.TransactionType == TransactionType.Deposit)
            .OrderByDescending(t => t.CreatedDate)
            .FirstOrDefaultAsync();

        if (depositApprovalTransaction == null)
        {
            transactionCheck.Details = _localizer["Could not find the balance transaction record for this deposit"].Value;
            result.Checks.Add(transactionCheck);
            result.Message = _localizer["Cannot determine when the deposit was approved"].Value;
            return result;
        }

        transactionCheck.Passed = true;
        transactionCheck.Details = _localizer["Deposit was approved on {0}", depositApprovalTransaction.CreatedDate.ToLocalTime().ToString("g")].Value;
        result.Checks.Add(transactionCheck);

        var approvalDate = depositApprovalTransaction.CreatedDate;

        // 4. Coin alım/satım kontrolü
        var tradeCheck = new ReversalCheckItem
        {
            Description = _localizer["Trade Activity Check"].Value,
            Passed = false
        };

        var tradesAfterDeposit = await _context.Trades
            .Where(t => t.UserId == userId && t.CreatedDate > approvalDate)
            .ToListAsync();

        if (tradesAfterDeposit.Any())
        {
            tradeCheck.Details = _localizer["User has made {0} trades after this deposit was approved", tradesAfterDeposit.Count].Value;
        }
        else
        {
            tradeCheck.Passed = true;
            tradeCheck.Details = _localizer["No trades have been made after this deposit was approved"].Value;
        }

        result.Checks.Add(tradeCheck);

        if (!tradeCheck.Passed)
        {
            result.Message = _localizer["Cannot reverse deposit because user has made trades after this deposit was approved"].Value;
            return result;
        }

        // 5. Para çekme kontrolü
        var withdrawalCheck = new ReversalCheckItem
        {
            Description = _localizer["Withdrawal Activity Check"].Value,
            Passed = false
        };

        var withdrawalsAfterDeposit = await _context.Withdrawals
            .Where(w => w.UserId == userId && w.CreatedDate > approvalDate)
            .ToListAsync();

        if (withdrawalsAfterDeposit.Any())
        {
            withdrawalCheck.Details = _localizer["User has made {0} withdrawals after this deposit was approved", withdrawalsAfterDeposit.Count].Value;
        }
        else
        {
            withdrawalCheck.Passed = true;
            withdrawalCheck.Details = _localizer["No withdrawals have been made after this deposit was approved"].Value;
        }

        result.Checks.Add(withdrawalCheck);

        if (!withdrawalCheck.Passed)
        {
            result.Message = _localizer["Cannot reverse deposit because user has made withdrawals after this deposit was approved"].Value;
            return result;
        }

        // 6. Paket satın alma kontrolü
        var packageCheck = new ReversalCheckItem
        {
            Description = _localizer["Package Purchase Check"].Value,
            Passed = false
        };

        var packagesAfterDeposit = await _context.UserPackages
            .Where(p => p.UserId == userId && p.PurchaseDate > approvalDate)
            .ToListAsync();

        if (packagesAfterDeposit.Any())
        {
            packageCheck.Details = _localizer["User has purchased {0} packages after this deposit was approved", packagesAfterDeposit.Count].Value;
        }
        else
        {
            packageCheck.Passed = true;
            packageCheck.Details = _localizer["No packages have been purchased after this deposit was approved"].Value;
        }

        result.Checks.Add(packageCheck);

        if (!packageCheck.Passed)
        {
            result.Message = _localizer["Cannot reverse deposit because user has purchased packages after this deposit was approved"].Value;
            return result;
        }

        // 7. Bakiye yeterlilik kontrolü
        var balanceCheck = new ReversalCheckItem
        {
            Description = _localizer["Balance Sufficiency Check"].Value,
            Passed = deposit.User.Balance >= deposit.Amount
        };

        if (!balanceCheck.Passed)
        {
            balanceCheck.Details = _localizer["User's current balance ({0}) is less than deposit amount ({1})",
                deposit.User.Balance, deposit.Amount].Value;
        }
        else
        {
            balanceCheck.Details = _localizer["User has sufficient balance for reversal"].Value;
        }

        result.Checks.Add(balanceCheck);

        if (!balanceCheck.Passed)
        {
            result.Message = _localizer["Cannot reverse deposit because user's balance is insufficient"].Value;
            return result;
        }

        // Tüm kontroller başarılı, geri alma işlemi yapılabilir
        result.CanReverse = true;
        result.Message = _localizer["Deposit can be reversed"].Value;
        return result;
    }

    public async Task<bool> ReverseDepositAsync(int depositId)
    {
        var eligibilityResult = await CheckDepositReversalEligibilityAsync(depositId);

        if (!eligibilityResult.CanReverse)
            throw new InvalidOperationException(eligibilityResult.Message);

        // Deposit kaydını al
        var deposit = await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == depositId);

        if (deposit == null)
            throw new InvalidOperationException(_localizer["Deposit not found"].Value);

        // Para yatırma işleminin onaylandığı tarihi bul (BALANCE_TRANSACTION tablosundan)
        var depositTransaction = await _context.BalanceTransactions
            .Where(t => t.UserId == deposit.UserId &&
                       t.ReferenceId == depositId &&
                       t.ReferenceType == "Deposit" &&
                       t.TransactionType == TransactionType.Deposit)
            .OrderByDescending(t => t.CreatedDate)
            .FirstOrDefaultAsync();

        if (depositTransaction == null)
            throw new InvalidOperationException(_localizer["Could not find the balance transaction record for this deposit"].Value);

        using (var transaction = await _context.Database.BeginTransactionAsync())
        {
            try
            {
                // 1. BALANCE_TRANSACTION tablosundan ilgili kaydı sil
                _context.BalanceTransactions.Remove(depositTransaction);

                // 2. Kullanıcının bakiyesini işlem öncesi değerine geri döndür
                // PreviousBalance, para yatırma işleminden önceki bakiyeyi içerir
                deposit.User.Balance = depositTransaction.PreviousBalance;

                // 3. Deposit durumunu Pending olarak güncelle
                deposit.Status = DepositStatus.Pending;
                deposit.LastOnlineDate = null;
                deposit.ProcessStatus = "Submitted";

                // Değişiklikleri kaydet
                await _context.SaveChangesAsync();

                // Transaction'ı commit et
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                // Hata durumunda transaction'ı geri al
                await transaction.RollbackAsync();
                throw new InvalidOperationException(_localizer["Failed to reverse deposit: {0}", ex.Message].Value);
            }
        }
    }
}
