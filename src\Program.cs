using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Localization;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Data;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.BackgroundServices;
// Use fully qualified names for StaticConfig to avoid ambiguity
using Serilog;
using Serilog.Events;
using Serilog.Sinks.SystemConsole.Themes;

var builder = WebApplication.CreateBuilder(args);
var environment = builder.Environment.EnvironmentName;
var appSettingFileNameForEnv = $"appsettings.{environment}.json";

// Get URLs from configuration
var urls = builder.Configuration["ASPNETCORE_URLS"] ??
    builder.WebHost.GetSetting("urls") ??
    "http://localhost:5000;https://localhost:5001";

Console.WriteLine($"Application URLs: {urls}");
Console.WriteLine($"Environment : {environment}");
Console.WriteLine($"Required appsettings file is: {appSettingFileNameForEnv}");

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", true, true)
    .AddJsonFile(appSettingFileNameForEnv, true, true)
    .AddEnvironmentVariables();

var loggingDirectory = builder.Configuration["LoggingDirectory"] ?? "Logs";
//loggingDirectory not exists create be sure it is created
if (!Directory.Exists(loggingDirectory))
{
    Directory.CreateDirectory(loggingDirectory);
    //Logger.Information($"Logging directory created: {loggingDirectory}");
}
//dont use serilog log built in use microsoft logger


var logFilePath = Path.Combine(loggingDirectory, "app.log");
var logErrorFilePath = Path.Combine(loggingDirectory, "app-errors.log");
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .Enrich.WithMachineName()
    .Enrich.WithThreadId()
    .WriteTo.Console(
        theme: AnsiConsoleTheme.Literate, // or Code, Grayscale, or custom
        applyThemeToRedirectedOutput: true // <--- forces color even when redirected
    )
    .WriteTo.File(logFilePath, rollingInterval: RollingInterval.Day, fileSizeLimitBytes: 104857600,
        retainedFileCountLimit: 7)
    .WriteTo.File(logErrorFilePath, rollingInterval: RollingInterval.Day, fileSizeLimitBytes: 104857600,
        retainedFileCountLimit: 7, restrictedToMinimumLevel: LogEventLevel.Error)
    .CreateLogger();

Log.Verbose("Logging Test");
Log.Debug("Logging Test");
Log.Information("Logging Test");
Log.Warning("Logging Test");
Log.Error("Logging Test");
Log.Fatal("Logging Test");
// Add Serilog
builder.Host.UseSerilog();
// Localization
var supportedCultures = RazeWinComTr.Areas.Admin.StaticConfig.supportedCultures.Select(p => p.Name).ToArray();
var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(supportedCultures[0])
    .AddSupportedCultures(supportedCultures)
    .AddSupportedUICultures(supportedCultures);
builder.Services.AddLocalization(options => options.ResourcesPath = "Areas/Admin/Resources");
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture("tr");
    options.SupportedCultures = RazeWinComTr.Areas.Admin.StaticConfig.supportedCultures;
    options.SupportedUICultures = RazeWinComTr.Areas.Admin.StaticConfig.supportedCultures;
});
builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(@".\keys"))
    .SetApplicationName("WebSite-App");

builder.Services.AddHybridCache();
//enable _httpClientFactory
builder.Services.AddHttpClient();
// Database
var sqliteConnection = builder.Configuration.GetConnectionString("SQLite");
builder.Services.AddDbContext<AppDbContext>(c => c.UseSqlite(sqliteConnection));
// Add services to the container.
builder.Services.AddControllers().AddJsonOptions(options =>
                                {
                                    options.JsonSerializerOptions.PropertyNamingPolicy = null;
                                    options.JsonSerializerOptions.DictionaryKeyPolicy = null;
                                });
builder.Services.AddRazorPages()
      .AddRazorPagesOptions(options =>
      {
          options.Conventions.AuthorizeAreaFolder("admin", "/", RazeWinComTr.Areas.Admin.StaticConfig.AdminPolicyName);
          options.Conventions.AuthorizeAreaFolder("MyAccount", "/", RazeWinComTr.Areas.MyAccount.StaticConfig.UserPolicyName);
      })
    .AddViewLocalization()
   .AddDataAnnotationsLocalization(options =>
   {
       options.DataAnnotationLocalizerProvider = (type, factory) =>
           factory.Create(typeof(SharedResource));
   });//// Add Support for MVC
//builder.Services.AddControllersWithViews();

// Core Services
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<HttpContextHelper>();
builder.Services.AddScoped<MarketService>();
builder.Services.AddScoped<BitexenService>();
builder.Services.AddScoped<BtcTurkService>();
builder.Services.AddScoped<CryptoExchangeServiceFactory>();
builder.Services.AddScoped<BankService>();
builder.Services.AddScoped<IWalletService, WalletService>();
builder.Services.AddScoped<DepositService>();
builder.Services.AddScoped<SettingService>();
builder.Services.AddScoped<ITradeService, TradeService>();
builder.Services.AddScoped<WithdrawalService>();
builder.Services.AddScoped<ReferralService>();
builder.Services.AddScoped<ITokenPriceService, TokenPriceService>();
builder.Services.AddScoped<ReferralRewardService>();
builder.Services.AddScoped<SampleDataService>();
builder.Services.AddScoped<UserService>();

// Referral package system services
builder.Services.AddScoped<PackageService>();
builder.Services.AddScoped<UserPackageService>();
builder.Services.AddScoped<PackageRewardPercentageService>();
builder.Services.AddScoped<BalanceTransactionService>();
builder.Services.AddScoped<TransactionReversalService>();
builder.Services.AddScoped<UserService>();
// Register all email helpers - choose which one to use as the default implementation
builder.Services.AddScoped<EmailHelper>();
builder.Services.AddScoped<MailGunDirectEmailHelper>();
// Use EmailHelper as the default implementation of IEmailHelper
builder.Services.AddScoped<IEmailHelper>(sp => sp.GetRequiredService<EmailHelper>());
// Alternatively, you can use one of these as the default:
// builder.Services.AddScoped<IEmailHelper>(sp => sp.GetRequiredService<MailGunEmailHelper>());
// builder.Services.AddScoped<IEmailHelper>(sp => sp.GetRequiredService<MailGunDirectEmailHelper>());

builder.Services.AddSingleton<FileService>();

// Configure background services
builder.Services.Configure<CryptoExchangeBackgroundServiceOptions>(
    builder.Configuration.GetSection("BackgroundServices:CryptoExchange"));
builder.Services.AddHostedService<BitexenBackgroundService>();
builder.Services.AddHostedService<BTCTurkBackgroundService>();
builder.Services.AddAuthentication(options =>
{
    options.DefaultSignInScheme = AuthConstants.UserAuthenticationScheme;
    options.DefaultAuthenticateScheme = AuthConstants.UserAuthenticationScheme;
    options.DefaultChallengeScheme = AuthConstants.UserAuthenticationScheme;
})
//.AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
//{
//    // Default cookie settings for all users
//    options.Cookie.Name = AuthConstants.UserCookieName;
//    options.LoginPath = "/Login";
//    options.LogoutPath = "/Logout";
//    options.AccessDeniedPath = "/AccessDenied";
//    options.ExpireTimeSpan = TimeSpan.FromDays(30);
//    options.SlidingExpiration = true;
//})
.AddCookie(AuthConstants.AdminAuthenticationScheme, options =>
{
    options.LoginPath = "/Admin/Account/Login";
    options.LogoutPath = "/Admin/Account/Logout";
    options.AccessDeniedPath = "/Admin/Account/AccessDenied";
    options.Cookie.Name = AuthConstants.AdminCookieName;
    options.ExpireTimeSpan = TimeSpan.FromDays(30);
})
.AddCookie(AuthConstants.UserAuthenticationScheme, options =>
{
    options.LoginPath = "/Login";
    options.LogoutPath = "/Logout";
    options.AccessDeniedPath = "/AccessDenied";
    options.Cookie.Name = AuthConstants.UserCookieName;
    options.ExpireTimeSpan = TimeSpan.FromDays(30);
});
// Authorization
builder.Services.AddAuthorization(options =>
{
    //options.AddPolicy(RazeWinComTr.Areas.Admin.StaticConfig.FormsPolicyName, policy =>
    //{
    //    policy.AuthenticationSchemes.Add(CookieAuthenticationDefaults.AuthenticationScheme);
    //    policy.RequireAuthenticatedUser();
    //});

    options.AddPolicy(RazeWinComTr.Areas.Admin.StaticConfig.AdminPolicyName, policy =>
    {
        policy.AuthenticationSchemes.Add(AuthConstants.AdminAuthenticationScheme);
        policy.RequireAuthenticatedUser();
        policy.RequireRole(Roller.Admin.ToString());
    });

    options.AddPolicy(RazeWinComTr.Areas.MyAccount.StaticConfig.UserPolicyName, policy =>
    {
        policy.AuthenticationSchemes.Add(AuthConstants.UserAuthenticationScheme);
        policy.RequireAuthenticatedUser();
        policy.RequireRole(Roller.User.ToString());
    });
});
// Session Configuration
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});
// Validate FileStoragePath exists
var fileStoragePath = builder.Configuration.GetValue<string?>("FileStoragePath");
var autoCreateFileStoragePath = builder.Configuration.GetValue<bool>("FileStoragePathAutoCreate", false);
if (autoCreateFileStoragePath)
{
    Log.Information("FileStoragePathAutoCreate is set to TRUE (+).");
    if (!string.IsNullOrEmpty(fileStoragePath) && !Directory.Exists(fileStoragePath))
    {
        Directory.CreateDirectory(fileStoragePath);

    }
    else
    {
        Log.Information(@"FileStoragePath is already exists. {fileStoragePath}");
    }
}
else
{
    Log.Information("FileStoragePathAutoCreate is not configured or set to false (-)");
}


if (string.IsNullOrEmpty(fileStoragePath) || !Directory.Exists(fileStoragePath))
{
    Log.Fatal("FileStoragePath is not configured or does not exist.");
    return;
}

//be sure sqlite database file's directory exists
var connectionString = builder.Configuration.GetConnectionString("SQLite");
// Extract database file path
string dbFilePath = new SqliteConnectionStringBuilder(connectionString).DataSource;

// Ensure directory exists
string? dbFileDirectory = Path.GetDirectoryName(dbFilePath);
if (!string.IsNullOrEmpty(dbFileDirectory) && !Directory.Exists(dbFileDirectory))
{
    Directory.CreateDirectory(dbFileDirectory);
    Log.Information($"Database file directory created: {dbFileDirectory}");
}
else
{
    Log.Information($"Database file directory already exists: {dbFileDirectory}");
}
//be sure Tus:StorageDiskPath exists
var tusStorageDiskPath = builder.Configuration.GetValue<string?>("Tus:StorageDiskPath");
if (string.IsNullOrEmpty(tusStorageDiskPath))
{
    Log.Fatal("Tus:StorageDiskPath is not configured.");
}
else if (!Directory.Exists(tusStorageDiskPath))
{
    Log.Warning("Tus:StorageDiskPath does not exist, creating it.");
    Directory.CreateDirectory(tusStorageDiskPath);
    Log.Information($"Tus:StorageDiskPath directory created: {tusStorageDiskPath}");
}
var useHttpsRedirection = builder.Configuration.GetValue<bool>("useHttpsRedirection", true);
var useExceptionHandler = builder.Configuration.GetValue<bool>("useExceptionHandler", true);
var useDeveloperExceptionPage = builder.Configuration.GetValue<bool>("useDeveloperExceptionPage", true);

var app = builder.Build();
// Database Initialization
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    var referralService = scope.ServiceProvider.GetRequiredService<ReferralService>();
    var sampleDataService = scope.ServiceProvider.GetRequiredService<SampleDataService>();
    var tokenPriceSercice = scope.ServiceProvider.GetRequiredService<ITokenPriceService>();
    var referralRewardService = scope.ServiceProvider.GetRequiredService<ReferralRewardService>();

    await dbContext.Database.MigrateAsync();
    await DbInitializer.InitializeAsync(dbContext, referralService, sampleDataService, tokenPriceSercice, referralRewardService, builder.Configuration.GetValue<string?>("FileStoragePath"));
}
// Configure the HTTP request pipeline.
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});
if (useDeveloperExceptionPage)
    app.UseDeveloperExceptionPage();
//if (useExceptionHandler)
//    app.UseExceptionHandler("/Error", true);

if (!app.Environment.IsDevelopment())
{
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

if (useHttpsRedirection)
{
    app.UseHttpsRedirection();
}

app.UseStaticFiles();

app.UseRouting();

app.UseRequestLocalization(localizationOptions);

app.UseAntiforgery();

app.UseAuthorization();

app.UseSession();

// Bakım modu middleware'ini ekle
app.UseMiddleware<RazeWinComTr.Middleware.MaintenanceModeMiddleware>();

app.MapRazorPages();

app.MapControllers();

await app.RunAsync();
