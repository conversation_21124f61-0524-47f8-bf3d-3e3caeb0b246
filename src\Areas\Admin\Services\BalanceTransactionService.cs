using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;
using RazeWinComTr.Areas.Admin.ViewModels.ReferralReward;

namespace RazeWinComTr.Areas.Admin.Services;

public class BalanceTransactionService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public BalanceTransactionService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _localizer = localizer;
    }

    public async Task<BalanceTransaction?> GetByIdAsync(int id)
    {
        return await _context.BalanceTransactions
            .Include(t => t.User)
            .FirstOrDefaultAsync(t => t.Id == id && t.IsActive);
    }

    public async Task<List<BalanceTransactionViewModel>> GetListAsync()
    {
        return await _context.BalanceTransactions
            .Include(t => t.User)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.ReferredUser)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.Package)
            .Where(t => t.IsActive)
            .Select(t => new BalanceTransactionViewModel
            {
                Id = t.Id,
                UserId = t.UserId,
                UserEmail = t.User != null ? t.User.Email : string.Empty,
                UserFullName = t.User != null ? $"{t.User.Name} {t.User.Surname}" : string.Empty,
                TransactionType = t.TransactionType,
                Amount = t.Amount,
                PreviousBalance = t.PreviousBalance,
                NewBalance = t.NewBalance,
                ReferenceId = t.ReferenceId,
                ReferenceType = t.ReferenceType,
                Description = t.Description,
                CreatedDate = t.CreatedDate,
                IsActive = t.IsActive,
                ReferralReward = ReferralRewardDetailsViewModel.FromEntity(t.ReferralReward)
            })
            .OrderByDescending(t => t.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<BalanceTransactionViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.BalanceTransactions
            .Include(t => t.User)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.ReferredUser)
            .Include(t => t.ReferralReward)
                .ThenInclude(r => r!.Package)
            .Where(t => t.UserId == userId && t.IsActive)
            .Select(t => new BalanceTransactionViewModel
            {
                Id = t.Id,
                UserId = t.UserId,
                UserEmail = t.User != null ? t.User.Email : string.Empty,
                UserFullName = t.User != null ? $"{t.User.Name} {t.User.Surname}" : string.Empty,
                TransactionType = t.TransactionType,
                Amount = t.Amount,
                PreviousBalance = t.PreviousBalance,
                NewBalance = t.NewBalance,
                ReferenceId = t.ReferenceId,
                ReferenceType = t.ReferenceType,
                Description = t.Description,
                CreatedDate = t.CreatedDate,
                IsActive = t.IsActive,
                ReferralReward = ReferralRewardDetailsViewModel.FromEntity(t.ReferralReward)
            })
            .OrderByDescending(t => t.CreatedDate)
            .ToListAsync();
    }

    public async Task<BalanceTransaction> CreateAsync(BalanceTransaction transaction)
    {
        transaction.IsActive = true;
        _context.BalanceTransactions.Add(transaction);
        await _context.SaveChangesAsync();
        return transaction;
    }

    public async Task<BalanceTransaction> RecordTransactionAsync(
        int userId,
        string transactionType,
        decimal amount,
        string? description = null,
        int? referenceId = null,
        string? referenceType = null,
        AppDbContext? existingContext = null)
    {

        var contextToUse = existingContext ?? _context;
        // Get user's current balance
        var user = await contextToUse.Users.FindAsync(userId);
        if (user == null)
            throw new Exception(_localizer["User not found"].Value);

        decimal previousBalance = user.Balance;
        decimal newBalance = previousBalance;

        // Update balance based on transaction type
        if (transactionType == TransactionType.Deposit ||
            transactionType == TransactionType.ReferralReward ||
            transactionType == TransactionType.Sell)
        {
            newBalance += amount;
        }
        else if (transactionType == TransactionType.Withdrawal ||
                 transactionType == TransactionType.PackagePurchase ||
                 transactionType == TransactionType.Buy)
        {
            if (previousBalance < amount)
                throw new Exception(_localizer["Insufficient balance"].Value);

            newBalance -= amount;
        }

        // Create balance transaction record
        var balanceTransaction = new BalanceTransaction
        {
            IsActive = true,
            UserId = userId,
            TransactionType = transactionType,
            Amount = amount,
            PreviousBalance = previousBalance,
            NewBalance = newBalance,
            ReferenceId = referenceId,
            ReferenceType = referenceType,
            Description = description,
            CreatedDate = DateTime.UtcNow
        };

        contextToUse.BalanceTransactions.Add(balanceTransaction);

        // Update user's balance
        user.Balance = newBalance;
        user.ModDate = DateTime.UtcNow;
        contextToUse.Users.Update(user);

        await contextToUse.SaveChangesAsync();
        return balanceTransaction;
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.BalanceTransactions.FindAsync(id);
        if (entity != null)
        {
            // Soft delete - set IsActive to false instead of removing
            entity.IsActive = false;
            await _context.SaveChangesAsync();
        }
    }

    public async Task UpdateAsync(BalanceTransaction transaction)
    {
        _context.Entry(transaction).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }
}
