using FileSignatures;

namespace RazeWinComTr.Areas.Admin.Helpers;

public static class ContentTypesHelper
{
    private static readonly Dictionary<string, string> _mimeTypeMap = new(StringComparer.OrdinalIgnoreCase)
    {
        ["avif"] = "image/avif",
        ["bmp"] = "image/bmp",
        ["cgm"] = "image/cgm",
        ["g3"] = "image/g3fax",
        ["gif"] = "image/gif",
        ["ief"] = "image/ief",
        ["jpeg"] = "image/jpeg",
        ["jpg"] = "image/jpeg",
        ["jpe"] = "image/jpeg",
        ["jxl"] = "image/jxl",
        ["ktx"] = "image/ktx",
        ["png"] = "image/png",
        ["btif"] = "image/prs.btif",
        ["sgi"] = "image/sgi",
        ["svg"] = "image/svg+xml",
        ["svgz"] = "image/svg+xml",
        ["tiff"] = "image/tiff",
        ["tif"] = "image/tiff",
        ["psd"] = "image/vnd.adobe.photoshop",
        ["uvi"] = "image/vnd.dece.graphic",
        ["uvvi"] = "image/vnd.dece.graphic",
        ["uvg"] = "image/vnd.dece.graphic",
        ["uvvg"] = "image/vnd.dece.graphic",
        ["djvu"] = "image/vnd.djvu",
        ["djv"] = "image/vnd.djvu",
        ["sub"] = "image/vnd.dvb.subtitle",
        ["dwg"] = "image/vnd.dwg",
        ["dxf"] = "image/vnd.dxf",
        ["fbs"] = "image/vnd.fastbidsheet",
        ["fpx"] = "image/vnd.fpx",
        ["fst"] = "image/vnd.fst",
        ["mmr"] = "image/vnd.fujixerox.edmics-mmr",
        ["rlc"] = "image/vnd.fujixerox.edmics-rlc",
        ["mdi"] = "image/vnd.ms-modi",
        ["wdp"] = "image/vnd.ms-photo",
        ["npx"] = "image/vnd.net-fpx",
        ["wbmp"] = "image/vnd.wap.wbmp",
        ["xif"] = "image/vnd.xiff",
        ["webp"] = "image/webp",
        ["3ds"] = "image/x-3ds",
        ["ras"] = "image/x-cmu-raster",
        ["cmx"] = "image/x-cmx",
        ["fh"] = "image/x-freehand",
        ["fhc"] = "image/x-freehand",
        ["fh4"] = "image/x-freehand",
        ["fh5"] = "image/x-freehand",
        ["fh7"] = "image/x-freehand",
        ["ico"] = "image/x-icon",
        ["sid"] = "image/x-mrsid-image",
        ["pcx"] = "image/x-pcx",
        ["pic"] = "image/x-pict",
        ["pct"] = "image/x-pict",
        ["pnm"] = "image/x-portable-anymap",
        ["pbm"] = "image/x-portable-bitmap",
        ["pgm"] = "image/x-portable-graymap",
        ["ppm"] = "image/x-portable-pixmap",
        ["rgb"] = "image/x-rgb",
        ["tga"] = "image/x-tga",
        ["xbm"] = "image/x-xbitmap",
        ["xpm"] = "image/x-xpixmap",
        ["xwd"] = "image/x-xwindowdump",

        ["3gp"] = "video/3gpp",
        ["3g2"] = "video/3gpp2",
        ["h261"] = "video/h261",
        ["h263"] = "video/h263",
        ["h264"] = "video/h264",
        ["jpgv"] = "video/jpeg",
        ["jpm"] = "video/jpm",
        ["jpgm"] = "video/jpm",
        ["mj2"] = "video/mj2",
        ["mjp2"] = "video/mj2",
        ["ts"] = "video/mp2t",
        ["m2t"] = "video/mp2t",
        ["m2ts"] = "video/mp2t",
        ["mts"] = "video/mp2t",
        ["mp4"] = "video/mp4",
        ["mp4v"] = "video/mp4",
        ["mpg4"] = "video/mp4",
        ["mpeg"] = "video/mpeg",
        ["mpg"] = "video/mpeg",
        ["mpe"] = "video/mpeg",
        ["m1v"] = "video/mpeg",
        ["m2v"] = "video/mpeg",
        ["ogv"] = "video/ogg",
        ["qt"] = "video/quicktime",
        ["mov"] = "video/quicktime",
        ["uvh"] = "video/vnd.dece.hd",
        ["uvvh"] = "video/vnd.dece.hd",
        ["uvm"] = "video/vnd.dece.mobile",
        ["uvvm"] = "video/vnd.dece.mobile",
        ["uvp"] = "video/vnd.dece.pd",
        ["uvvp"] = "video/vnd.dece.pd",
        ["uvs"] = "video/vnd.dece.sd",
        ["uvvs"] = "video/vnd.dece.sd",
        ["uvv"] = "video/vnd.dece.video",
        ["uvvv"] = "video/vnd.dece.video",
        ["dvb"] = "video/vnd.dvb.file",
        ["fvt"] = "video/vnd.fvt",
        ["mxu"] = "video/vnd.mpegurl",
        ["m4u"] = "video/vnd.mpegurl",
        ["pyv"] = "video/vnd.ms-playready.media.pyv",
        ["uvu"] = "video/vnd.uvvu.mp4",
        ["uvvu"] = "video/vnd.uvvu.mp4",
        ["viv"] = "video/vnd.vivo",
        ["webm"] = "video/webm",
        ["f4v"] = "video/x-f4v",
        ["fli"] = "video/x-fli",
        ["flv"] = "video/x-flv",
        ["m4v"] = "video/x-m4v",
        ["mkv"] = "video/x-matroska",
        ["mk3d"] = "video/x-matroska",
        ["mks"] = "video/x-matroska",
        ["mng"] = "video/x-mng",
        ["asf"] = "video/x-ms-asf",
        ["asx"] = "video/x-ms-asf",
        ["vob"] = "video/x-ms-vob",
        ["wm"] = "video/x-ms-wm",
        ["wmv"] = "video/x-ms-wmv",
        ["wmx"] = "video/x-ms-wmx",
        ["wvx"] = "video/x-ms-wvx",
        ["avi"] = "video/x-msvideo",
        ["movie"] = "video/x-sgi-movie",
        ["smv"] = "video/x-smv"
    };

    public static List<string> ImageExtensions()
    {
        return _mimeTypeMap.Where(p => p.Value.StartsWith("image/")).Select(p => p.Key).ToList();
    }

    public static List<string> VideoExtensions()
    {
        return _mimeTypeMap.Where(p => p.Value.StartsWith("video/")).Select(p => p.Key).ToList();
    }

    public static List<string> ImageContentTypes()
    {
        return _mimeTypeMap.Where(p => p.Value.StartsWith("image/")).Select(p => p.Value).ToList();
    }

    public static List<string> VideoContentTypes()
    {
        return _mimeTypeMap.Where(p => p.Value.StartsWith("video/")).Select(p => p.Value).ToList();
    }


    public static MimeTypeDetectedData? DetermineFileFormat(Stream stream)
    {
        // You need to reset the position of the stream before using MimeKit if it's been read already
        if (stream.CanSeek) stream.Seek(0, SeekOrigin.Begin);

        var inspector = new FileFormatInspector();
        var format = inspector.DetermineFileFormat(stream);
        if (format == null) return null;
        return new MimeTypeDetectedData
        {
            Extension = format.Extension,
            ContentType = format.MediaType
        };
    }
    public static string GetImageContentType(string fileName)
    {
        var ext = Path.GetExtension(fileName).ToLowerInvariant();
        return ext switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".webp" => "image/webp",
            _ => "application/octet-stream"
        };
    }
}

public class MimeTypeDetectedData
{
    public string ContentType { get; set; } = string.Empty;
    public string Extension { get; set; } = string.Empty;
}