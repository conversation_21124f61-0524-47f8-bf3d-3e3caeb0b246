using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Services;

public class UserPackageService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<UserPackageService> _logger;
    private readonly BalanceTransactionService _balanceTransactionService;
    private readonly IWalletService _walletService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly ITradeService _tradeService;

    public UserPackageService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer,
        ILogger<UserPackageService> logger,
        BalanceTransactionService balanceTransactionService,
        IWalletService walletService,
        ITokenPriceService tokenPriceService,
        ITradeService tradeService)
    {
        _context = context;
        _localizer = localizer;
        _logger = logger;
        _balanceTransactionService = balanceTransactionService;
        _walletService = walletService;
        _tokenPriceService = tokenPriceService;
        _tradeService = tradeService;
    }

    public async Task<UserPackage?> GetByIdAsync(int id)
    {
        return await _context.UserPackages
            .Include(up => up.Package)
            .Include(up => up.User)
            .FirstOrDefaultAsync(up => up.Id == id);
    }

    public async Task<UserPackage?> GetActivePackageForUserAsync(int userId)
    {
        return await _context.UserPackages
            .Include(up => up.Package)
            .Where(up => up.UserId == userId && up.Status == UserPackageStatus.Active)
            .OrderByDescending(up => up.PurchaseDate)
            .FirstOrDefaultAsync();
    }

    public async Task<List<UserPackageViewModel>> GetListAsync()
    {
        return await _context.UserPackages
            .Include(up => up.Package)
            .Include(up => up.User)
            .Select(up => new UserPackageViewModel
            {
                Id = up.Id,
                UserId = up.UserId,
                UserEmail = up.User.Email,
                UserFullName = $"{up.User.Name} {up.User.Surname}",
                PackageId = up.PackageId,
                PackageName = up.Package.Name,
                PurchaseDate = up.PurchaseDate,
                ExpiryDate = up.ExpiryDate,
                BalanceTransactionId = up.BalanceTransactionId,
                Status = up.Status,
                CreatedDate = up.CreatedDate,
                ModifiedDate = up.ModifiedDate
            })
            .OrderByDescending(up => up.PurchaseDate)
            .ToListAsync();
    }

    public async Task<List<UserPackageViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.UserPackages
            .Include(up => up.Package)
            .Where(up => up.UserId == userId)
            .Select(up => new UserPackageViewModel
            {
                Id = up.Id,
                UserId = up.UserId,
                PackageId = up.PackageId,
                PackageName = up.Package.Name,
                PurchaseDate = up.PurchaseDate,
                ExpiryDate = up.ExpiryDate,
                BalanceTransactionId = up.BalanceTransactionId,
                Status = up.Status,
                CreatedDate = up.CreatedDate,
                ModifiedDate = up.ModifiedDate
            })
            .OrderByDescending(up => up.PurchaseDate)
            .ToListAsync();
    }

    public async Task<List<UserPackageViewModel>> GetUserPackagesAsync(int userId)
    {
        return await _context.UserPackages
            .Include(up => up.Package)
            .Include(up => up.User)
            .Where(up => up.UserId == userId)
            .Select(up => new UserPackageViewModel
            {
                Id = up.Id,
                UserId = up.UserId,
                UserEmail = up.User.Email,
                UserFullName = $"{up.User.Name} {up.User.Surname}",
                PackageId = up.PackageId,
                PackageName = up.Package.Name,
                PurchaseDate = up.PurchaseDate,
                ExpiryDate = up.ExpiryDate,
                BalanceTransactionId = up.BalanceTransactionId,
                Status = up.Status,
                CreatedDate = up.CreatedDate,
                ModifiedDate = up.ModifiedDate
            })
            .OrderByDescending(up => up.PurchaseDate)
            .ToListAsync();
    }

    public async Task<UserPackage> PurchasePackageAsync(int userId, int packageId, int? transactionId = null, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;


        var package = await contextToUse.Packages.FindAsync(packageId);
        if (package == null)
            throw new Exception(_localizer["Package not found"].Value);

        var user = await contextToUse.Users.FindAsync(userId);
        if (user == null)
            throw new Exception(_localizer["User not found"].Value);

        // Find and deactivate any existing active packages for this user
        var activePackages = await contextToUse.UserPackages
            .Where(up => up.UserId == userId && up.Status == UserPackageStatus.Active)
            .ToListAsync();

        foreach (var activePackage in activePackages)
        {
            activePackage.Status = UserPackageStatus.Cancelled;
            activePackage.ModifiedDate = DateTime.UtcNow;
            _logger.LogInformation("Deactivating previous package ID {PackageId} for user {UserId} due to new package purchase",
                activePackage.PackageId, userId);
        }

        if (activePackages.Any())
        {
            await contextToUse.SaveChangesAsync();
        }

        // Create user package record
        var userPackage = new UserPackage
        {
            UserId = userId,
            PackageId = packageId,
            PurchaseDate = DateTime.UtcNow,
            BalanceTransactionId = transactionId, // Using the transaction ID as reference
            Status = UserPackageStatus.Active
        };

        contextToUse.UserPackages.Add(userPackage);
        await contextToUse.SaveChangesAsync();

        // Update user balance and record the balance transaction for the package purchase
        try
        {
            // Check if user has enough balance
            if (user.Balance < package.Price)
                throw new Exception(_localizer["Insufficient balance"].Value);

            decimal previousBalance = user.Balance;
            decimal newBalance = previousBalance - package.Price;

            // Update user's balance
            user.Balance = newBalance;
            user.ModDate = DateTime.UtcNow;
            contextToUse.Users.Update(user);

            // Record the balance transaction
            var savedBalanceTransaction = await _balanceTransactionService.RecordTransactionAsync(
              userId: userId,
              transactionType: TransactionType.PackagePurchase,
              amount: package.Price,
              previousBalance: previousBalance,
              newBalance: newBalance,
              description: _localizer["Purchase of {0} package", package.Name].Value,
              referenceId: packageId,
              referenceType: BalanceTransactionReferenceTypes.Package,
              existingContext: contextToUse
            );
            userPackage.BalanceTransactionId = savedBalanceTransaction.Id; //bind userPackage and BalanceTransaction
            await contextToUse.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording balance transaction for package purchase by user {UserId}, package {PackageId}", userId, packageId);
            throw; // Re-throw the exception since balance operations are critical
        }

        // Add RZW tokens equal to the package price to the user's wallet
        try
        {
            // Get RZW token information (ID and price) in a single query
            var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
            int rzwTokenId = rzwTokenInfo.TokenId;
            decimal rzwBuyPrice = rzwTokenInfo.BuyPrice;

            // Calculate how many RZW tokens to add based on the package price and RZW price
            decimal packagePriceInTL = package.Price;
            decimal rzwAmount = packagePriceInTL / rzwBuyPrice;

            _logger.LogInformation("Calculating RZW tokens for package purchase: {PackagePrice} TL / {RzwBuyPrice} TL/RZW = {RzwAmount} RZW",
                packagePriceInTL, rzwBuyPrice, rzwAmount);

            // Get the user's current RZW wallet balance
            var currentWalletBalance = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenId);

            // Add RZW tokens to the user's wallet
            var updatedWallet = await _walletService.AddAvailableBalanceAsync(userId, rzwTokenInfo, rzwAmount, TradeType.PackageBonus, contextToUse);

            // Get the user's TRY balance (we don't change it for bonus tokens, but need it for the trade record)
            var userTryBalance = user.Balance;

            // Create a trade record for the RZW tokens added as package bonus
            var trade = new Trade
            {
                Type = TradeType.PackageBonus,
                UserId = userId,
                CoinId = rzwTokenId,
                CoinRate = rzwBuyPrice, // Use the actual RZW buy price
                CoinAmount = rzwAmount,
                TryAmount = packagePriceInTL, // The TL amount is the package price
                PreviousCoinBalance = currentWalletBalance,
                NewCoinBalance = updatedWallet.Balance,

                PreviousBalance = userTryBalance, // TRY balance doesn't change
                NewBalance = userTryBalance, // TRY balance doesn't change

                PreviousWalletBalance = currentWalletBalance,
                NewWalletBalance = updatedWallet.Balance,
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };

            await _tradeService.CreateAsync(trade);

            _logger.LogInformation("Added {Amount} RZW tokens to wallet of user {UserId} for package purchase. RZW Token ID: {RzwTokenId}. Trade record created.",
                rzwAmount, userId, rzwTokenId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding RZW tokens to wallet for user {UserId}, package {PackageId}",
                userId, packageId);
            throw new Exception(_localizer["Error"].Value, ex);
        }

        return userPackage;

    }

    public async Task<UserPackage> CreateAsync(UserPackage userPackage)
    {
        _context.UserPackages.Add(userPackage);
        await _context.SaveChangesAsync();
        return userPackage;
    }

    public async Task<UserPackage> UpdateAsync(UserPackage userPackage)
    {
        userPackage.ModifiedDate = DateTime.UtcNow;
        _context.UserPackages.Update(userPackage);
        await _context.SaveChangesAsync();
        return userPackage;
    }

    public async Task DeleteAsync(int id)
    {
        var userPackage = await _context.UserPackages.FindAsync(id);
        if (userPackage != null)
        {
            // Check if there are any referral rewards associated with this user package
            var hasReferralRewards = await _context.ReferralRewards.AnyAsync(rr =>
                rr.PackageId == userPackage.PackageId && rr.ReferredUserId == userPackage.UserId);

            if (hasReferralRewards)
            {
                throw new InvalidOperationException(_localizer["Cannot delete user package because it has associated referral rewards. Please remove referral rewards first."].Value);
            }

            _context.UserPackages.Remove(userPackage);
            await _context.SaveChangesAsync();
        }
    }
}
