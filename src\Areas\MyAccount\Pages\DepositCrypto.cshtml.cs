using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class DepositCryptoModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly IWalletService _walletService;
        private readonly MarketService _marketService;
        private readonly DepositService _depositService;

        public DepositCryptoModel(
            AppDbContext context,
            IStringLocalizer<SharedResource> localizer,
            IWalletService walletService,
            MarketService marketService,
            DepositService depositService)
        {
            _context = context;
            _localizer = localizer;
            _walletService = walletService;
            _marketService = marketService;
            _depositService = depositService;
            DepositInfo = new CryptoDepositViewModel();
        }

        [BindProperty]
        public CryptoDepositViewModel DepositInfo { get; set; }

        public string? SuccessMessage { get; set; }
        public string? ErrorMessage { get; set; }
        public int? CurrentUserId { get; set; }
        public string? UserFullName { get; set; }

        public async Task<IActionResult> OnGetAsync(string coin)
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            CurrentUserId = userId.Value;

            // Get user information
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            UserFullName = $"{user.Name} {user.Surname}";

            // Set the selected cryptocurrency type based on the query parameter
            if (!string.IsNullOrEmpty(coin))
            {
                // Validate that the coin type is one of the supported types
                if (coin == "BTC" || coin == "USDT-TRC20" || coin == "BNB-BEP20")
                {
                    DepositInfo.CoinType = coin;
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            CurrentUserId = userId.Value;

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Get user information
                var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    return NotFound();
                }

                // Get the combined amount from the integer and fractional parts
                decimal amount = DepositInfo.GetCombinedAmount();

                // Validate Amount
                if (amount <= 0)
                {
                    ModelState.AddModelError("DepositInfo.AmountInteger", _localizer["Please enter a valid amount"]);
                    return Page();
                }

                // Check minimum deposit amount based on cryptocurrency type
                decimal minimumAmount = 0;
                switch (DepositInfo.CoinType)
                {
                    case "BTC":
                        minimumAmount = 0.0001m; // Minimum BTC deposit
                        break;
                    case "USDT-TRC20":
                        minimumAmount = 10m; // Minimum USDT deposit
                        break;
                    case "BNB-BEP20":
                        minimumAmount = 0.01m; // Minimum BNB deposit
                        break;
                }

                if (amount < minimumAmount)
                {
                    ModelState.AddModelError("DepositInfo.AmountInteger",
                        _localizer["Minimum deposit amount for {0} is {1}",
                            DepositInfo.CoinType,
                            minimumAmount.ToString("0.########")]);
                    return Page();
                }

                // Get the market ID for the selected cryptocurrency
                int? coinId = null;
                string coinCode = "";

                switch (DepositInfo.CoinType)
                {
                    case "BTC":
                        coinCode = "BTC";
                        break;
                    case "USDT-TRC20":
                        coinCode = "USDT";
                        break;
                    case "BNB-BEP20":
                        coinCode = "BNB";
                        break;
                    default:
                        ModelState.AddModelError("DepositInfo.CoinType", _localizer["Invalid cryptocurrency type"]);
                        return Page();
                }

                var market = await _context.Markets.FirstOrDefaultAsync(m => m.Coin == coinCode && m.IsActive == 1);
                if (market == null)
                {
                    ModelState.AddModelError("DepositInfo.CoinType", _localizer["Selected cryptocurrency is not available"]);
                    return Page();
                }

                coinId = market.Id;

                // Create extra data for the crypto deposit
                var extraData = new CryptoDepositExtraData
                {
                    CoinType = DepositInfo.CoinType,
                    TransactionHash = DepositInfo.TransactionHash,
                    SenderAddress = DepositInfo.SenderAddress,
                    CoinId = coinId.Value,
                    CoinCode = coinCode
                };

                // Create deposit record
                var deposit = new Deposit
                {
                    UserId = userId.Value,
                    DepositType = $"Crypto - {DepositInfo.CoinType}",
                    Amount = amount,
                    FullName = $"{user.Name} {user.Surname}",
                    ExtraData = JsonSerializer.Serialize(extraData),
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                    CreatedDate = DateTime.UtcNow,
                    ProcessStatus = "Submitted",
                    Status = DepositStatus.Pending
                };

                // Use the deposit service to create the deposit
                // This ensures it's only added to balance history when approved
                await _depositService.CreateAsync(deposit);

                // Set success message
                SuccessMessage = _localizer["Your cryptocurrency deposit request has been received. It will be processed as soon as possible."].Value;

                // Clear the form
                DepositInfo = new CryptoDepositViewModel();

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
                return Page();
            }
        }
    }

    public class CryptoDepositViewModel
    {
        [Required(ErrorMessage = "Cryptocurrency type is required")]
        [Display(Name = "Cryptocurrency Type")]
        public string CoinType { get; set; } = "BTC";

        [Required(ErrorMessage = "Transaction hash is required")]
        [Display(Name = "Transaction Hash")]
        public string TransactionHash { get; set; } = string.Empty;

        // Integer part of the amount
        [Required(ErrorMessage = "Amount is required")]
        [Display(Name = "Amount")]
        [Range(0, int.MaxValue, ErrorMessage = "Please enter a valid amount")]
        public int AmountInteger { get; set; }

        // Fractional part of the amount (as string to preserve leading zeros)
        [Display(Name = "Amount (Fraction)")]
        [StringLength(8, ErrorMessage = "Fractional part cannot exceed 8 digits")]
        public string AmountFraction { get; set; } = "00000000";

        [Display(Name = "Sender Address")]
        public string SenderAddress { get; set; } = string.Empty;

        [Display(Name = "Notes")]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Gets the combined decimal amount from the integer and fractional parts
        /// </summary>
        /// <returns>The combined decimal amount in invariant culture</returns>
        public decimal GetCombinedAmount()
        {
            // Combine the integer and fractional parts
            string combinedString = $"{AmountInteger}.{AmountFraction}";

            // Parse using invariant culture
            if (decimal.TryParse(combinedString, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            // Fallback to just the integer part if parsing fails
            return AmountInteger;
        }
    }

    public class CryptoDepositExtraData
    {
        public string CoinType { get; set; } = string.Empty;
        public string TransactionHash { get; set; } = string.Empty;
        public string SenderAddress { get; set; } = string.Empty;
        public int CoinId { get; set; }
        public string CoinCode { get; set; } = string.Empty;
    }
}
