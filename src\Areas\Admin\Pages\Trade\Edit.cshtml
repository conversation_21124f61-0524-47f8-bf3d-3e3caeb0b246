@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Trade"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Trade"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/trade">@L["Trades"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <input type="hidden" asp-for="Entity.UserId"/>
                <input type="hidden" asp-for="Entity.CoinId"/>
                <input type="hidden" asp-for="Entity.Type"/>
                <div class="card-body">
                    <div class="form-group">
                        <label>@L["User"]</label>
                        <input type="text" class="form-control" value="@Model.UserEmail" readonly />
                    </div>
                    <div class="form-group">
                        <label>@L["Coin"]</label>
                        <input type="text" class="form-control" value="@Model.CoinName" readonly />
                    </div>
                    <div class="form-group">
                        <label>@L["Type"]</label>
                        @{
                            string tradeTypeText = Model.Entity.Type switch
                            {
                                (int)TradeType.Buy => L["Buy"],
                                (int)TradeType.Sell => L["Sell"],
                                (int)TradeType.PackageBonus => L["Package Bonus"],
                                (int)TradeType.ReferralReward => L["Referral Reward"],
                                _ => Model.Entity.Type.ToString()
                            };
                        }
                        <input type="text" class="form-control" value="@tradeTypeText" readonly />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Amount">@L["Amount"]</label>
                        <input asp-for="Entity.Amount" class="form-control" type="number" step="0.00000001" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Rate">@L["Rate"]</label>
                        <input asp-for="Entity.Rate" class="form-control" type="number" step="0.00000001" required/>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> @L["Warning: Editing a trade will not update user balances. Use with caution."]
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="/admin/trade" class="btn btn-default">
                            <i class="fas fa-arrow-left mr-1"></i> @L["Cancel"]
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i> @L["Save"]
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

@section Styles {
    <style>
        .text-muted.small {
            white-space: normal;
            line-height: 1.4;
        }
    </style>
}
