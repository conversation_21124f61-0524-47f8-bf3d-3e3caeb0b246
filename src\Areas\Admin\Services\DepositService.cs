using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;
using System.Text.Json;

namespace RazeWinComTr.Areas.Admin.Services;

public class DepositService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IWalletService _walletService;
    private readonly BalanceTransactionService _balanceTransactionService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly ILogger<DepositService> _logger;

    public DepositService(
        IStringLocalizer<SharedResource> localizer,
        AppDbContext context,
        IWalletService walletService,
        BalanceTransactionService balanceTransactionService,
        ITokenPriceService tokenPriceService,
        ILogger<DepositService> logger)
    {
        _localizer = localizer;
        _context = context;
        _walletService = walletService;
        _balanceTransactionService = balanceTransactionService;
        _tokenPriceService = tokenPriceService;
        _logger = logger;
    }



    public async Task<Deposit?> GetByIdAsync(int id)
    {
        return await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<DepositViewModel>> GetListAsync(string? paymentTypeFilter = null)
    {
        var query = _context.Deposits
            .Include(p => p.User)
            .AsQueryable();

        // Apply payment type filter if provided
        if (!string.IsNullOrEmpty(paymentTypeFilter))
        {
            query = query.Where(p => p.DepositType.StartsWith(paymentTypeFilter));
        }

        return await query
            .Select(p => new DepositViewModel
            {
                Id = p.Id,
                UserId = p.UserId,
                UserEmail = p.User.Email,
                DepositType = p.DepositType,
                Amount = p.Amount,
                FullName = p.FullName,
                ProcessStatus = p.ProcessStatus ?? string.Empty,
                Status = p.Status,
                CreatedDate = p.CreatedDate,
                LastOnlineDate = p.LastOnlineDate,
                IsCryptoDeposit = p.DepositType.StartsWith("Crypto -"),
                RewardStatus = p.RewardStatus
            })
            .OrderByDescending(p => p.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<DepositViewModel>> GetCryptoDepositsAsync()
    {
        return await GetListAsync("Crypto -");
    }

    public async Task<List<DepositViewModel>> GetFiatDepositsAsync()
    {
        var allDeposits = await GetListAsync();
        return allDeposits.Where(p => !p.IsCryptoDeposit).ToList();
    }

    public async Task<List<DepositViewModel>> GetApprovedDepositsAsync()
    {
        return await _context.Deposits
            .Include(p => p.User)
            .Where(p => p.Status == DepositStatus.Approved)
            .Select(p => new DepositViewModel
            {
                Id = p.Id,
                UserId = p.UserId,
                UserEmail = p.User.Email,
                DepositType = p.DepositType,
                Amount = p.Amount,
                FullName = p.FullName,
                ProcessStatus = p.ProcessStatus ?? string.Empty,
                Status = p.Status,
                CreatedDate = p.CreatedDate,
                LastOnlineDate = p.LastOnlineDate,
                IsCryptoDeposit = p.DepositType.StartsWith("Crypto -"),
                RewardStatus = p.RewardStatus
            })
            .OrderByDescending(p => p.CreatedDate)
            .ToListAsync();
    }

    public async Task<DepositViewModel?> GetDepositViewModelByIdAsync(int id)
    {
        return await _context.Deposits
            .Include(p => p.User)
            .Where(p => p.Id == id)
            .Select(p => new DepositViewModel
            {
                Id = p.Id,
                UserId = p.UserId,
                UserEmail = p.User.Email,
                DepositType = p.DepositType,
                Amount = p.Amount,
                FullName = p.FullName,
                ProcessStatus = p.ProcessStatus ?? string.Empty,
                Status = p.Status,
                CreatedDate = p.CreatedDate,
                LastOnlineDate = p.LastOnlineDate,
                IsCryptoDeposit = p.DepositType.StartsWith("Crypto -"),
                RewardStatus = p.RewardStatus
            })
            .FirstOrDefaultAsync();
    }

    public async Task<List<DepositViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.Deposits
            .Where(p => p.UserId == userId)
            .Select(p => new DepositViewModel
            {
                Id = p.Id,
                UserId = p.UserId,
                UserEmail = p.User.Email,
                DepositType = p.DepositType,
                Amount = p.Amount,
                FullName = p.FullName,
                ProcessStatus = p.ProcessStatus ?? string.Empty,
                Status = p.Status,
                CreatedDate = p.CreatedDate,
                LastOnlineDate = p.LastOnlineDate
            })
            .OrderByDescending(p => p.CreatedDate)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Deposits.FindAsync(id);
        if (entity != null)
        {
            _context.Deposits.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Deposit> CreateAsync(Deposit deposit)
    {
        _context.Deposits.Add(deposit);
        await _context.SaveChangesAsync();
        return deposit;
    }

    public async Task<Deposit> CreateWithBalanceUpdateAsync(Deposit deposit)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            _context.Deposits.Add(deposit);
            await _context.SaveChangesAsync();

            // Only add to balance history if the deposit is approved
            if (deposit.Status == DepositStatus.Approved)
            {
                bool isCryptoDeposit = deposit.DepositType.StartsWith("Crypto -");

                if (isCryptoDeposit)
                {
                    // Handle cryptocurrency deposit
                    await HandleCryptoDepositApprovalAsync(deposit);
                }
                else
                {
                    // Get user and update balance
                    var user = await _context.Users.FindAsync(deposit.UserId);
                    if (user == null)
                        throw new Exception(_localizer["User not found"].Value);

                    decimal previousBalance = user.Balance;
                    decimal newBalance = previousBalance + deposit.Amount;

                    // Update user's balance
                    user.Balance = newBalance;
                    user.ModDate = DateTime.UtcNow;
                    _context.Users.Update(user);

                    // Record the balance transaction
                    await _balanceTransactionService.RecordTransactionAsync(
                        userId: deposit.UserId,
                        transactionType: TransactionType.Deposit,
                        amount: deposit.Amount,
                        previousBalance: previousBalance,
                        newBalance: newBalance,
                        description: _localizer["Deposit: {0}", deposit.DepositType].Value,
                        referenceId: deposit.Id,
                        referenceType: BalanceTransactionReferenceTypes.Deposit,
                        existingContext: _context
                    );

                    // Set reward status to pending when payment is approved
                    deposit.RewardStatus = DepositRewardStatus.Pending;
                    await _context.SaveChangesAsync();
                }
            }

            await transaction.CommitAsync();
            return deposit;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task UpdateAsync(Deposit deposit)
    {
        _context.Entry(deposit).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task<bool> UpdateDepositStatusAsync(int depositId, DepositStatus newStatus)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var deposit = await _context.Deposits
                .Include(p => p.User)
                .FirstOrDefaultAsync(p => p.Id == depositId);

            if (deposit == null)
                return false;

            var previousStatus = deposit.Status;

            // If status hasn't changed, just return true
            if (previousStatus == newStatus)
                return true;

            // Update payment status
            deposit.Status = newStatus;
            deposit.LastOnlineDate = DateTime.UtcNow;
            deposit.ProcessStatus = newStatus == DepositStatus.Approved ? "Completed" :
                                   newStatus == DepositStatus.Rejected ? "Rejected" : "Pending";

            // Check if this is a cryptocurrency deposit
            bool isCryptoDeposit = deposit.DepositType.StartsWith("Crypto -");

            // If changing from pending/rejected to approved
            if (newStatus == DepositStatus.Approved && previousStatus != DepositStatus.Approved)
            {
                if (isCryptoDeposit)
                {
                    // Handle cryptocurrency deposit
                    await HandleCryptoDepositApprovalAsync(deposit);
                }
                else
                {
                    // Get user and update balance
                    var user = await _context.Users.FindAsync(deposit.UserId);
                    if (user == null)
                        throw new Exception(_localizer["User not found"].Value);

                    decimal previousBalance = user.Balance;
                    decimal newBalance = previousBalance + deposit.Amount;

                    // Update user's balance
                    user.Balance = newBalance;
                    user.ModDate = DateTime.UtcNow;
                    _context.Users.Update(user);

                    // Record the balance transaction
                    await _balanceTransactionService.RecordTransactionAsync(
                        userId: deposit.UserId,
                        transactionType: TransactionType.Deposit,
                        amount: deposit.Amount,
                        previousBalance: previousBalance,
                        newBalance: newBalance,
                        description: _localizer["Deposit: {0}", deposit.DepositType].Value,
                        referenceId: deposit.Id,
                        referenceType: BalanceTransactionReferenceTypes.Deposit,
                        existingContext: _context
                    );

                    // Set reward status to pending when payment is approved
                    // Rewards will be distributed manually via the Distribute Rewards button
                    deposit.RewardStatus = DepositRewardStatus.Pending;
                }
            }
            // If changing from approved to pending/rejected
            else if (newStatus != DepositStatus.Approved && previousStatus == DepositStatus.Approved)
            {
                if (isCryptoDeposit)
                {
                    // Handle cryptocurrency deposit reversal
                    await HandleCryptoDepositReversalAsync(deposit);
                }
                else
                {
                    // Get user and update balance for deposit reversal
                    var user = await _context.Users.FindAsync(deposit.UserId);
                    if (user == null)
                        throw new Exception(_localizer["User not found"].Value);

                    decimal previousBalance = user.Balance;
                    decimal newBalance = previousBalance - deposit.Amount;

                    // Check if user has enough balance for reversal
                    if (newBalance < 0)
                    {
                        // If there's not enough balance, set it to 0
                        newBalance = 0;
                    }

                    // Update user's balance
                    user.Balance = newBalance;
                    user.ModDate = DateTime.UtcNow;
                    _context.Users.Update(user);

                    // Record the balance transaction
                    await _balanceTransactionService.RecordTransactionAsync(
                        userId: deposit.UserId,
                        transactionType: TransactionType.Withdrawal,
                        amount: deposit.Amount,
                        previousBalance: previousBalance,
                        newBalance: newBalance,
                        description: _localizer["Deposit reversal: {0}", deposit.DepositType].Value,
                        referenceId: deposit.Id,
                        referenceType: BalanceTransactionReferenceTypes.Deposit,
                        existingContext: _context
                    );
                }
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Handles the approval of a cryptocurrency deposit
    /// </summary>
    private async Task HandleCryptoDepositApprovalAsync(Deposit deposit)
    {
        // Extract the cryptocurrency information from the payment's extra data
        if (string.IsNullOrEmpty(deposit.ExtraData))
            throw new InvalidOperationException(_localizer["Missing cryptocurrency information in payment data"].Value);

        try
        {
            // Deserialize the extra data
            var extraData = JsonSerializer.Deserialize<CryptoDepositExtraData>(deposit.ExtraData);
            if (extraData == null)
                throw new InvalidOperationException(_localizer["Invalid cryptocurrency data format"].Value);

            // Get the coin ID
            int coinId = extraData.CoinId;

            var coinInfo = await _tokenPriceService.GetCoinInfoAsync(coinId);

            // Add the cryptocurrency to the user's wallet
            await _walletService.AddAvailableBalanceAsync(deposit.UserId, coinInfo, deposit.Amount, TradeType.CryptoDeposit);
        }
        catch (JsonException)
        {
            throw new InvalidOperationException(_localizer["Could not parse cryptocurrency data"].Value);
        }
    }

    /// <summary>
    /// Handles the reversal of a cryptocurrency deposit
    /// </summary>
    private async Task HandleCryptoDepositReversalAsync(Deposit deposit)
    {
        // Extract the cryptocurrency information from the payment's extra data
        if (string.IsNullOrEmpty(deposit.ExtraData))
            throw new InvalidOperationException(_localizer["Missing cryptocurrency information in payment data"].Value);

        try
        {
            // Deserialize the extra data
            var extraData = JsonSerializer.Deserialize<CryptoDepositExtraData>(deposit.ExtraData);
            if (extraData == null)
                throw new InvalidOperationException(_localizer["Invalid cryptocurrency data format"].Value);

            // Get the coin ID
            int coinId = extraData.CoinId;
            var coinInfo = await _tokenPriceService.GetCoinInfoAsync(coinId);
            // Deduct the cryptocurrency from the user's wallet
            bool success = await _walletService.DeductAvailableBalanceAsync(deposit.UserId, coinInfo, deposit.Amount, _context);
            if (!success)
                throw new InvalidOperationException(_localizer["Insufficient cryptocurrency balance for reversal"].Value);
        }
        catch (JsonException)
        {
            throw new InvalidOperationException(_localizer["Could not parse cryptocurrency data"].Value);
        }
    }

    /// <summary>
    /// Data class for cryptocurrency deposit extra data
    /// </summary>
    private class CryptoDepositExtraData
    {
        public string CoinType { get; set; } = string.Empty;
        public string TransactionHash { get; set; } = string.Empty;
        public string SenderAddress { get; set; } = string.Empty;
        public int CoinId { get; set; }
        public string CoinCode { get; set; } = string.Empty;
    }
}
