using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;

namespace RazeWinComTr.Tests.Services
{
    public class UserPackageServiceTests : TestBase
    {
        private readonly Mock<IStringLocalizer<SharedResource>> _localizerMock;
        private readonly Mock<ILogger<UserPackageService>> _loggerMock;

        public UserPackageServiceTests()
        {
            _localizerMock = new Mock<IStringLocalizer<SharedResource>>();
            _loggerMock = new Mock<ILogger<UserPackageService>>();

            // Setup localizer to return the key as the value for simplicity
            _localizerMock.Setup(l => l[It.IsAny<string>()])
                .Returns((string key) => new LocalizedString(key, key));
            _localizerMock.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
        }

        /// <summary>
        /// Test case: User with 100,000 TL balance purchases 20,000 TL Platinum package
        /// Expected results:
        /// - User balance should be 80,000 TL after purchase (100,000 - 20,000)
        /// - RZW wallet should have 3,527.33686067 RZW tokens (20,000 / 5.67)
        /// - Trade history should have "Package Bonus" record with:
        ///   * PreviousCoinBalance: 0 RZW → NewCoinBalance: 3,527.33686067 RZW
        ///   * PreviousBalance: 80,000 TL → NewBalance: 80,000 TL (bonus doesn't change TRY balance)
        /// - Balance history should have "Package Purchase" -20,000 TL record
        /// </summary>
        [Fact]
        public async Task PurchasePackageAsync_WithValidUserAndPackage_CreatesUserPackageSuccessfully()
        {
            // Arrange
            var dbContext = CreateDbContext("PurchasePackageAsync_WithValidUserAndPackage_CreatesUserPackageSuccessfully");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2); // RZW price is 5.67 TL, token ID is 2
            var mockTradeService = CreateMockTradeService();
            var mockBalanceTransactionService = new Mock<BalanceTransactionService>(dbContext, _localizerMock.Object);

            // Create test user with 100,000 TL balance
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 100000m;
            dbContext.Users.Add(user);

            // Create test package (Platinum - 20,000 TL)
            var package = TestDataGenerator.CreatePackage(1, "Platinum", 20000m);
            dbContext.Packages.Add(package);

            // Create RZW market data
            var rzwMarket = new Market
            {
                Id = 2, // RZW token ID
                Coin = "RZW", // Required property
                Name = "RazeWin",
                ShortName = "RZW", // Required property
                PairCode = "RZW",
                BuyPrice = 5.67m,
                SellPrice = 5.67m,
                IsActive = 1, // Market.IsActive is int, not bool
                CrDate = DateTime.UtcNow
            };
            dbContext.Markets.Add(rzwMarket);

            await dbContext.SaveChangesAsync();

            var service = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object);

            // Act
            var result = await service.PurchasePackageAsync(1, 1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal(1, result.PackageId);
            Assert.Equal(UserPackageStatus.Active, result.Status);

            // Verify user balance was deducted (100,000 - 20,000 = 80,000)
            var updatedUser = await dbContext.Users.FindAsync(1);
            Assert.NotNull(updatedUser);
            Assert.Equal(80000m, updatedUser.Balance);

            // Verify RZW tokens were added to wallet (20,000 / 5.67 = 3527.33686067)
            var expectedRzwAmount = 20000m / 5.67m;
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == 1),
                It.Is<RzwTokenInfo>(info => info.TokenId == 2),
                It.Is<decimal>(amount => Math.Abs(amount - expectedRzwAmount) <= 0.00000001m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);

            // Verify trade record was created for package bonus
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Once);
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == 1 &&
                t.CoinId == 2 && // RZW token ID
                t.Type == TradeType.PackageBonus &&
                t.CoinRate == 5.67m &&
                Math.Abs(t.CoinAmount - expectedRzwAmount) < 0.00000001m && // 8 decimal places precision
                t.TryAmount == 20000m &&
                t.PreviousCoinBalance == 0m &&
                Math.Abs(t.NewCoinBalance - expectedRzwAmount) < 0.00000001m &&
                t.PreviousBalance == 80000m && // TRY balance after package purchase (80,000 TL)
                t.NewBalance == 80000m // Package bonus doesn't change TRY balance (stays 80,000 TL)
            )), Times.Once);

            // Verify balance transaction was recorded
            var balanceTransaction = await dbContext.BalanceTransactions
                .FirstOrDefaultAsync(bt => bt.UserId == 1 && bt.TransactionType == TransactionType.PackagePurchase);
            Assert.NotNull(balanceTransaction);
            Assert.Equal(20000m, balanceTransaction.Amount);
            Assert.Equal(100000m, balanceTransaction.PreviousBalance);
            Assert.Equal(80000m, balanceTransaction.NewBalance);
            Assert.Equal(1, balanceTransaction.ReferenceId); // Package ID
            Assert.Equal(BalanceTransactionReferenceTypes.Package, balanceTransaction.ReferenceType);
        }
    }
}
