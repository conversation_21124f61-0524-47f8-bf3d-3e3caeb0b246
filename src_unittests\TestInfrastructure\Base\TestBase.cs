using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Tests.Mocks;
using RazeWinComTr.Tests.TestInfrastructure.Factories;

using RazeWinComTr.Tests.TestInfrastructure.Utilities;

namespace RazeWinComTr.Tests.TestInfrastructure.Base
{
    /// <summary>
    /// Base class for all test classes in the RazeWinComTr.Tests namespace.
    /// Provides common functionality for creating test database contexts, mock services, and loggers.
    /// </summary>
    public abstract class TestBase
    {
        /// <summary>
        /// Creates a new in-memory database context for testing.
        /// Each test should use a unique database name to ensure test isolation.
        /// </summary>
        /// <param name="databaseName">A unique name for the in-memory database</param>
        /// <returns>A new AppDbContext instance connected to an in-memory database</returns>
        protected AppDbContext CreateDbContext(string databaseName)
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName)
                .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;

            var context = new AppDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
            return context;
        }



        /// <summary>
        /// Creates a mock WalletService for testing using Moq.
        /// The mock is configured with basic wallet operations and tracking capabilities.
        /// MEVCUT TESTLERLE UYUMLU - Değiştirmeyin!
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance</returns>
        protected Mock<IWalletService> CreateMockWalletService()
        {
            var mock = new Mock<IWalletService>();
            var wallets = new Dictionary<(int UserId, int CoinId), Wallet>();
            var nextId = 1;

            // Setup GetByIdAsync
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => wallets.Values.FirstOrDefault(w => w.Id == id));

            // Setup GetByUserIdAsync
            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int userId) => wallets.Values.Where(w => w.UserId == userId).ToList());

            // Setup GetTopNByUserIdAsync
            mock.Setup(x => x.GetTopNByUserIdAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync((int userId, int topN) => wallets.Values
                    .Where(w => w.UserId == userId)
                    .OrderByDescending(w => w.Balance)
                    .Take(topN)
                    .ToList());

            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, int coinId, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, coinId), out var wallet);
                    return wallet;
                });

            // Setup GetListAsync
            mock.Setup(x => x.GetListAsync())
                .ReturnsAsync(() => wallets.Values.Select(w => new WalletViewModel
                {
                    Id = w.Id,
                    UserId = w.UserId,
                    CoinId = w.CoinId,
                    Balance = w.Balance,
                    CreatedDate = w.CreatedDate,
                    ModifiedDate = w.ModifiedDate
                }).ToList());

            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((Wallet wallet, AppDbContext context) =>
                {
                    wallet.Id = nextId++;
                    wallets[(wallet.UserId, wallet.CoinId)] = wallet;
                    return wallet;
                });

            mock.Setup(x => x.UpdateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .Returns((Wallet wallet, AppDbContext context) =>
                {
                    if (wallets.ContainsKey((wallet.UserId, wallet.CoinId)))
                    {
                        wallets[(wallet.UserId, wallet.CoinId)] = wallet;
                    }
                    return Task.CompletedTask;
                });

            // Setup DeleteAsync
            mock.Setup(x => x.DeleteAsync(It.IsAny<int>()))
                .Returns((int id) =>
                {
                    var wallet = wallets.Values.FirstOrDefault(w => w.Id == id);
                    if (wallet != null)
                    {
                        wallets.Remove((wallet.UserId, wallet.CoinId));
                    }
                    return Task.CompletedTask;
                });

            // Setup GetUserAvailableBalanceAsync
            mock.Setup(x => x.GetUserAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync((int userId, int coinId) =>
                {
                    wallets.TryGetValue((userId, coinId), out var wallet);
                    return wallet?.Balance ?? 0;
                });

            // Setup AddAvailableBalanceAsync
            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, rzwTokenInfo.TokenId), out var wallet);

                    if (wallet == null)
                    {
                        wallet = new Wallet
                        {
                            Id = nextId++,
                            UserId = userId,
                            CoinId = rzwTokenInfo.TokenId,
                            Balance = amount,
                            CreatedDate = DateTime.UtcNow
                        };
                        wallets[(userId, rzwTokenInfo.TokenId)] = wallet;
                    }
                    else
                    {
                        wallet.Balance += amount;
                        wallet.ModifiedDate = DateTime.UtcNow;
                    }

                    return wallet;
                });

            // Setup DeductAvailableBalanceAsync
            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, rzwTokenInfo.TokenId), out var wallet);

                    if (wallet == null || wallet.Balance < amount)
                    {
                        return false;
                    }

                    wallet.Balance -= amount;
                    wallet.ModifiedDate = DateTime.UtcNow;
                    return true;
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock WalletService that throws exceptions for testing failure scenarios using Moq.
        /// All methods throw "Wallet service failure" exceptions to simulate service failures.
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance configured to throw exceptions</returns>
        protected Mock<IWalletService> CreateMockWalletServiceWithFailure()
        {
            var mock = new Mock<IWalletService>();
            var exception = new Exception("Wallet service failure");

            // Setup all methods to throw exceptions
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetTopNByUserIdAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetListAsync())
                .ThrowsAsync(exception);

            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.UpdateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.DeleteAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetUserAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            return mock;
        }



        /// <summary>
        /// Creates a legacy mock WalletService with failure for backward compatibility.
        /// Use CreateMockWalletServiceWithFailure() for new tests.
        /// </summary>
        /// <returns>A MockWalletServiceWithFailure instance</returns>
        [Obsolete("Use CreateMockWalletServiceWithFailure() with Moq framework instead")]
        protected MockWalletServiceWithFailure CreateLegacyMockWalletServiceWithFailure()
        {
            return new MockWalletServiceWithFailure();
        }





        // ==================== NEW STANDARDIZED FACTORY METHODS ====================

        /// <summary>
        /// Creates a mock TokenPriceService using the standardized factory.
        /// </summary>
        protected Mock<ITokenPriceService> CreateMockTokenPriceService(decimal buyPrice = 1.0m, int coinId = 1, decimal? sellPrice = null)
        {
            return MockServiceFactory.CreateTokenPriceService(buyPrice, coinId, sellPrice);
        }

        /// <summary>
        /// Creates a mock TradeService using the standardized factory.
        /// </summary>
        protected Mock<ITradeService> CreateMockTradeService()
        {
            return MockServiceFactory.CreateTradeService();
        }

        /// <summary>
        /// Creates a mock Logger using the standardized factory.
        /// </summary>
        protected Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return MockServiceFactory.CreateLogger<T>();
        }

        /// <summary>
        /// Creates a mock StringLocalizer using the standardized factory.
        /// </summary>
        protected Mock<Microsoft.Extensions.Localization.IStringLocalizer<T>> CreateMockLocalizer<T>()
        {
            return MockServiceFactory.CreateLocalizer<T>();
        }

        // ==================== SIMPLE TEST DATA HELPERS ====================

        /// <summary>
        /// Creates a simple test user using natural .NET object initialization.
        /// </summary>
        protected User CreateUser(int userId = 1, string? email = null, string? referralCode = null, int? referrerId = null)
        {
            return TestDataHelpers.CreateUser(userId, email, referralCode, referrerId);
        }

        /// <summary>
        /// Creates a referral chain of users for testing using existing TestDataGenerator pattern.
        /// </summary>
        protected List<User> CreateReferralChain(int count, int baseUserId = 1)
        {
            // Use existing TestDataGenerator for backward compatibility
            var users = new List<User>();
            for (int i = 0; i < count; i++)
            {
                var userId = baseUserId + i;
                var referrerId = i > 0 ? baseUserId + i - 1 : (int?)null;
                users.Add(TestDataHelpers.CreateUserLegacy(userId, $"REF{userId}", referrerId));
            }
            return users;
        }

        /// <summary>
        /// Creates standard packages for testing.
        /// </summary>
        protected List<Package> CreateStandardPackages()
        {
            return TestDataHelpers.CreateAllStandardPackages();
        }

        /// <summary>
        /// Creates standard reward percentages for all packages.
        /// </summary>
        protected List<PackageRewardPercentage> CreateStandardRewardPercentages()
        {
            return TestDataHelpers.CreateAllStandardRewardPercentages();
        }

        // ==================== TEST UTILITIES ====================

        /// <summary>
        /// Seeds the database context with standard test data using simple object creation.
        /// </summary>
        protected async Task SeedStandardTestDataAsync(AppDbContext context)
        {
            // Add standard packages
            var packages = CreateStandardPackages();
            context.Packages.AddRange(packages);

            // Add reward percentages
            var rewardPercentages = CreateStandardRewardPercentages();
            context.PackageRewardPercentages.AddRange(rewardPercentages);

            // Add a basic referral chain
            var users = CreateReferralChain(5);
            context.Users.AddRange(users);

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Creates a unique database name for test isolation.
        /// </summary>
        protected string CreateUniqueDatabaseName(string? testName = null)
        {
            var timestamp = DateTime.UtcNow.Ticks;
            var randomPart = Guid.NewGuid().ToString("N")[..8];
            return $"TestDb_{testName ?? "Test"}_{timestamp}_{randomPart}";
        }

        /// <summary>
        /// Asserts that two decimal values are equal within a small tolerance.
        /// Useful for financial calculations where floating point precision matters.
        /// </summary>
        protected void AssertDecimalEqual(decimal expected, decimal actual, decimal tolerance = 0.00000001m)
        {
            var difference = Math.Abs(expected - actual);
            if (difference > tolerance)
            {
                throw new Xunit.Sdk.XunitException($"Expected {expected}, but got {actual}. Difference: {difference}");
            }
        }
    }
}
