using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class BalanceHistoryModel(
        BalanceTransactionService balanceTransactionService,
        IStringLocalizer<SharedResource> localizer) : PageModel
    {
        private readonly BalanceTransactionService _balanceTransactionService = balanceTransactionService;
        private readonly IStringLocalizer<SharedResource> _localizer = localizer;

        public List<BalanceTransactionViewModel> Transactions { get; set; } = [];
        public decimal CurrentBalance { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return RedirectToPage("/Login", new { area = "", returnUrl = "/MyAccount/BalanceHistory" });
            }

            // Get user's balance transactions
            Transactions = await _balanceTransactionService.GetByUserIdAsync(userId.Value);

            // Get current balance from the most recent transaction
            if (Transactions.Any())
            {
                CurrentBalance = Transactions.OrderByDescending(t => t.CreatedDate).First().NewBalance;
            }

            return Page();
        }
    }
}
