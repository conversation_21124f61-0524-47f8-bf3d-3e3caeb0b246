using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Helpers;
using RazeWinComTr.Models;
using RazeWinComTr.ViewModels.Market;
using System.Security.Claims;

namespace RazeWinComTr.Controllers
{
    [Route("api/ajax")]
    [ApiController]
    public class MarketController : ControllerBase
    {
        private readonly MarketService _marketService;
        private readonly BitexenService _bitexenService;
        private readonly BtcTurkService _btcTurkService;

        private readonly SampleDataService _sampleDataService;
        private readonly AppDbContext _dbContext;
        private readonly ILogger<MarketController> _logger;
        private readonly IWalletService _walletService;
        private readonly ITradeService _tradeService;
        private readonly BalanceTransactionService _balanceTransactionService;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public MarketController(
            MarketService marketService,
            BitexenService bitexenService,
            BtcTurkService bTCTurkService,
            SampleDataService sampleDataService,
            AppDbContext dbContext,
            ILogger<MarketController> logger,
            IWalletService walletService,
            ITradeService tradeService,
            BalanceTransactionService balanceTransactionService,
            IStringLocalizer<SharedResource> localizer)
        {
            _localizer = localizer;
            _btcTurkService = bTCTurkService;
            _bitexenService = bitexenService;
            _marketService = marketService;
            _sampleDataService = sampleDataService;
            _dbContext = dbContext;
            _logger = logger;
            _walletService = walletService;
            _tradeService = tradeService;
            _balanceTransactionService = balanceTransactionService;
        }

        /// <summary>
        /// Get available pairs from the specified API service
        /// </summary>
        /// <param name="apiServiceName">The name of the API service (Bitexen or BTCTurk)</param>
        /// <returns>A list of available pairs</returns>
        [HttpGet("pairs/{apiServiceName}")]
        public async Task<ActionResult<List<PairViewModel>>> GetAvailablePairsAsync(string apiServiceName)
        {
            try
            {
                if (string.IsNullOrEmpty(apiServiceName))
                {
                    return BadRequest("API service name is required");
                }

                List<PairViewModel> pairs = new List<PairViewModel>();

                // Get pairs from the specified API service
                if (apiServiceName.Equals(ApiServiceNames.Bitexen, StringComparison.OrdinalIgnoreCase))
                {
                    pairs = await _bitexenService.GetAvailablePairsAsync();
                }
                else if (apiServiceName.Equals(ApiServiceNames.BTCTurk, StringComparison.OrdinalIgnoreCase))
                {
                    pairs = await _btcTurkService.GetAvailablePairsAsync();
                }
                else
                {
                    return BadRequest("Invalid API service name");
                }

                return Ok(pairs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available pairs from {ApiServiceName}", apiServiceName);
                return StatusCode(500, "An error occurred while retrieving available pairs");
            }
        }

        [HttpGet("coinlist")]
        public async Task<ActionResult<IEnumerable<CoinListViewModel>>> GetCoinListAsync()
        {
            var markets = await _marketService.GetListAsync(isActive: 1);
            var now = DateTime.UtcNow;
            var priceThresholdSeconds = 30; // Default threshold

            // Try to get the threshold from configuration
            if (HttpContext.RequestServices.GetService<IConfiguration>() is IConfiguration config)
            {
                priceThresholdSeconds = config.GetValue("BackgroundServices:CryptoExchange:PriceThresholdSeconds", 30);
            }

            //// Check if any market needs a price update
            //var marketsNeedingUpdate = markets.Where(m =>
            //    m.IsApi == 1 &&
            //    (m.LastPriceUpdate == null ||
            //     (now - m.LastPriceUpdate.Value).TotalSeconds > priceThresholdSeconds)
            //).ToList();

            //if (marketsNeedingUpdate.Count > 0)
            //{
            //    _logger.LogInformation("{Count} markets need price updates", marketsNeedingUpdate.Count);

            //    // Group markets by API service name
            //    var marketGroups = marketsNeedingUpdate
            //        .Where(m => !string.IsNullOrEmpty(m.ApiServiceName)) // Only include markets with a specified API service
            //        .GroupBy(m => m.ApiServiceName!)
            //        .ToDictionary(g => g.Key, g => g.ToList());

            //    // Update BTCTurk markets if needed
            //    if (marketGroups.TryGetValue(ApiServiceNames.BTCTurk, out var btcTurkMarkets) && btcTurkMarkets.Count > 0)
            //    {
            //        var tickerResponse = await _btcTurkService.GetTickerAsync();
            //        if (tickerResponse != null)
            //        {
            //            await _btcTurkService.UpdateBuySellPricesAsync(tickerResponse);
            //        }
            //    }

            //    // Update Bitexen markets if needed
            //    if (marketGroups.TryGetValue(ApiServiceNames.Bitexen, out var bitexenMarkets) && bitexenMarkets.Count > 0)
            //    {
            //        var tickerResponse = await _bitexenService.GetTickerAsync();
            //        if (tickerResponse != null)
            //        {
            //            await _bitexenService.UpdateBuySellPricesAsync(tickerResponse);
            //        }
            //    }

            //    // Refresh market data after updates
            //    markets = await _marketService.GetListAsync();
            //}

            // Apply general increase to prices
            foreach (var market in markets)
            {
                market.BuyPrice += market.GeneralIncrease;
                market.SellPrice += market.GeneralIncrease;

                // Format the prices without rounding, but don't add thousands separators here
                // We'll handle the thousands separators in the view
                market.BuyPriceFormatted = Areas.Admin.Helpers.NumberFormatHelper.FormatDecimal(market.BuyPrice, market.DecimalPlaces);
                market.SellPriceFormatted = Areas.Admin.Helpers.NumberFormatHelper.FormatDecimal(market.SellPrice, market.DecimalPlaces);
            }

            // Map to the ViewModel to exclude sensitive or unnecessary data
            var viewModels = markets.Select(m => new CoinListViewModel
            {
                Id = m.Id,
                Coin = m.Coin,
                Name = m.Name,
                BuyPrice = m.BuyPrice,
                SellPrice = m.SellPrice,
                // Store the exact price values as strings to prevent rounding
                // We'll handle the thousands separators in the view
                BuyPriceFormatted = Areas.Admin.Helpers.NumberFormatHelper.FormatDecimal(m.BuyPrice, m.DecimalPlaces),
                SellPriceFormatted = Areas.Admin.Helpers.NumberFormatHelper.FormatDecimal(m.SellPrice, m.DecimalPlaces),
                Change24h = m.Change24h,
                DecimalPlaces = m.DecimalPlaces,
                IconUrl = m.IconUrl,
                PairCode = m.PairCode,
                LastPriceUpdate = m.LastPriceUpdate
            });

            return Ok(viewModels);
        }

        /// <summary>
        /// Get detailed information about a specific coin for a user
        /// </summary>
        /// <param name="request">The request containing the coin code</param>
        /// <returns>Coin details with user-specific pricing</returns>
        [HttpPost("coinsingle")]
        public async Task<ActionResult<CoinSingleResponse>> GetCoinSingleAsync([FromForm] CoinSingleRequest request)
        {

            var response = new CoinSingleResponse();

            try
            {
                // Check if user is authenticated
                if (!User.Identity?.IsAuthenticated ?? true)
                {
                    return Ok(response); // Return error response for unauthenticated users
                }

                // Get user ID from claims
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Ok(response); // Return error response if user ID not found
                }

                // Get the market/coin by code
                var market = await _dbContext.Markets.FirstOrDefaultAsync(m => m.PairCode == request.Kod);
                if (market == null)
                {
                    return Ok(response); // Return error response if market not found
                }

                // Get user from database to check for special market pricing
                var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    return Ok(response); // Return error response if user not found
                }

                // Calculate prices with general increase
                decimal buyPrice = market.BuyPrice + market.GeneralIncrease;
                decimal sellPrice = market.SellPrice + market.GeneralIncrease;

                // Get user's special market settings if any
                string? specialMarketJson = user.SpecialMarket;

                // Apply special pricing if available
                var specialPricing = MarketPricingHelper.GetSpecialMarketPricing(market.PairCode, specialMarketJson);
                if (specialPricing != null)
                {
                    (buyPrice, sellPrice) = MarketPricingHelper.ApplySpecialPricing(buyPrice, sellPrice, specialPricing);
                }

                // Determine image URL
                string imageUrl = string.Empty;
                if (!string.IsNullOrEmpty(market.IconUrl))
                {
                    // Use the market's icon URL
                    imageUrl = FileService.getAnonymousFileUrl(nameof(Market), market.Id);
                }
                else
                {
                    // Default image
                    imageUrl = "/site/images/markets/default.png";
                }

                // Get user's balance
                decimal userBalance = 0;
                decimal coinBalance = 0;

                // Use the user object we already retrieved above
                userBalance = user.Balance;

                // Get coin balance
                var coinWallet = await _walletService.GetByUserIdAndCoinIdAsync(userId, market.Id);
                if (coinWallet != null)
                {
                    coinBalance = coinWallet.Balance;
                }
                else
                {
                    // Create wallet if it doesn't exist
                    var newWallet = new Wallet
                    {
                        UserId = userId,
                        CoinId = market.Id,
                        Balance = 0,
                        CreatedDate = DateTime.UtcNow
                    };
                    await _walletService.CreateAsync(newWallet);
                }

                // Prepare response
                response.Status = "success";
                response.Data = new CoinData
                {
                    ImageUrl = imageUrl,
                    CoinCode = market.Coin,
                    Name = market.Name,
                    BuyPrice = buyPrice,
                    SellPrice = sellPrice,
                    DecimalPlaces = market.DecimalPlaces,
                    MinimumBuy = market.MinimumBuy,
                    MaximumBuy = market.MaximumBuy,
                    MinimumSell = market.MinimumSell,
                    MaximumSell = market.MaximumSell
                };
                response.Balance = userBalance;
                response.CoinBalance = coinBalance;

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting coin details for {CoinCode}", request.Kod);
                return Ok(response); // Return error response on exception
            }
        }

        /// <summary>
        /// Buy coin with TRY balance
        /// </summary>
        /// <param name="request">The request containing coin code and amount</param>
        /// <returns>Success or error message</returns>
        [HttpPost("coinal")]
        public async Task<ActionResult<CoinTradeResponse>> BuyCoinAsync([FromForm] CoinTradeRequest request)
        {
            var response = new CoinTradeResponse();

            try
            {
                // Check if user is authenticated
                if (!User.Identity?.IsAuthenticated ?? true)
                {
                    response.Message = "OTURUM_YOK";
                    return Ok(response);
                }

                // Get user ID from claims
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    response.Message = "User ID not found";
                    return Ok(response);
                }

                // Get the combined amount from the integer and fractional parts
                decimal amount = request.GetCombinedAmount();

                // Validate amount
                if (amount <= 0)
                {
                    response.Message = "Harcanacak miktar 0'dan az olamaz";
                    return Ok(response);
                }

                // Get the market/coin by code
                var coin = await _dbContext.Markets.FirstOrDefaultAsync(m => m.Coin == request.Coin && m.IsActive == 1);
                if (coin == null)
                {
                    response.Message = "Coin bulunamadı [HATA2493]";
                    return Ok(response);
                }

                // Get user from database
                var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    response.Message = "User not found";
                    return Ok(response);
                }
                using var transaction = await _dbContext.Database.BeginTransactionAsync();
                // Calculate prices with general increase
                decimal buyPrice = coin.BuyPrice + coin.GeneralIncrease;
                decimal sellPrice = coin.SellPrice + coin.GeneralIncrease;

                // Get user's special market settings if any
                string? specialMarketJson = user.SpecialMarket;

                // Apply special pricing if available
                var specialPricing = MarketPricingHelper.GetSpecialMarketPricing(coin.PairCode, specialMarketJson);
                if (specialPricing != null)
                {
                    (buyPrice, sellPrice) = MarketPricingHelper.ApplySpecialPricing(buyPrice, sellPrice, specialPricing);
                }

                // Calculate coin amount to buy
                decimal coinAmountRaw = amount / buyPrice;
                decimal coinAmount = Math.Round(coinAmountRaw, coin.DecimalPlaces);

                // Get user's balance directly from User table
                decimal userBalance = user.Balance;

                // Check if user has enough balance
                if (amount > userBalance)
                {
                    response.Message = "Kullanılabilir bakiyenizi aştınız.";
                    return Ok(response);
                }

                // Check minimum and maximum buy limits
                if (coinAmount < coin.MinimumBuy)
                {
                    response.Message = $"<small>Minimum alınabilecek tutar: {coin.MinimumBuy} {coin.Coin} ({coin.MinimumBuy * buyPrice}$)</small>";
                    return Ok(response);
                }

                if (coinAmount > coin.MaximumBuy)
                {
                    response.Message = $"<small>Maksimum alınabilecek tutar: {coin.MaximumBuy} {coin.Coin} ({coin.MaximumBuy * buyPrice}$)</small>";
                    return Ok(response);
                }

                // Get or create coin wallet
                decimal previousCoinBalance = 0;
                decimal newCoinBalance = coinAmount;

                var coinWallet = await _walletService.GetByUserIdAndCoinIdAsync(userId, coin.Id);
                if (coinWallet != null)
                {
                    previousCoinBalance = coinWallet.Balance;
                    newCoinBalance = previousCoinBalance + coinAmount;

                    // Update coin wallet
                    coinWallet.Balance = newCoinBalance;
                    coinWallet.ModifiedDate = DateTime.UtcNow;
                    await _walletService.UpdateAsync(coinWallet);
                }
                else
                {
                    // Create new coin wallet
                    coinWallet = new Wallet
                    {
                        UserId = userId,
                        CoinId = coin.Id,
                        Balance = coinAmount,
                        CreatedDate = DateTime.UtcNow
                    };
                    await _walletService.CreateAsync(coinWallet);
                }

                // Update user's balance
                decimal previousBalance = userBalance;
                decimal newBalance = userBalance - amount;

                // Update user balance in database
                user.Balance = newBalance;
                user.ModDate = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync();

                // Create trade record
                var trade = new Trade
                {
                    Type = TradeType.Buy,
                    UserId = userId,
                    CoinId = coin.Id,
                    CoinRate = buyPrice,
                    CoinAmount = coinAmount,
                    TryAmount = amount,
                    PreviousCoinBalance = previousCoinBalance,
                    NewCoinBalance = newCoinBalance,
                    PreviousBalance = previousBalance,
                    NewBalance = newBalance,
                    PreviousWalletBalance = previousCoinBalance,
                    NewWalletBalance = coinWallet.Balance,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                var createdTrade = await _tradeService.CreateAsync(trade);

                // Create balance transaction record to track the decrease in balance
                // Record the balance transaction and update user's TRY balance
                await _balanceTransactionService.RecordTransactionAsync(
                    userId: userId,
                    transactionType: TransactionType.Buy,
                    amount: amount,
                    description: _localizer["Buy: {0}", coin.Name].Value,
                    referenceId: createdTrade.Id,
                    referenceType: BalanceTransactionReferenceTypes.Trade,
                    existingContext: _dbContext
                );
                await transaction.CommitAsync();
                // Return success response
                response.Status = "success";
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error buying coin {CoinCode}", request.Coin);
                response.Message = "An error occurred while processing your request.";
                return Ok(response);
            }
        }

        /// <summary>
        /// Sell coin to get TRY
        /// </summary>
        /// <param name="request">The request containing coin code and amount</param>
        /// <returns>Success or error message</returns>
        [HttpPost("coinsat")]
        public async Task<ActionResult<CoinTradeResponse>> SellCoinAsync([FromForm] CoinTradeRequest request)
        {
            var response = new CoinTradeResponse();

            try
            {
                // Check if user is authenticated
                if (!User.Identity?.IsAuthenticated ?? true)
                {
                    response.Message = "Alış/Satış emri girebilmek için üye girişi yapılmalıdır.";
                    return Ok(response);
                }

                // Get user ID from claims
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    response.Message = "User ID not found";
                    return Ok(response);
                }

                // Get the combined amount from the integer and fractional parts
                decimal amount = request.GetCombinedAmount();

                // Validate amount
                if (amount <= 0)
                {
                    response.Message = "Harcanacak miktar 0'dan az olamaz";
                    return Ok(response);
                }

                // Get the market/coin by code
                var coin = await _dbContext.Markets.FirstOrDefaultAsync(m => m.Coin == request.Coin && m.IsActive == 1);
                if (coin == null)
                {
                    response.Message = "Coin bulunamadı [HATA2493]";
                    return Ok(response);
                }
                using var transaction = await _dbContext.Database.BeginTransactionAsync();

                // Get user from database
                var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    response.Message = "User not found";
                    return Ok(response);
                }

                // Calculate prices with general increase
                decimal buyPrice = coin.BuyPrice + coin.GeneralIncrease;
                decimal sellPrice = coin.SellPrice + coin.GeneralIncrease;

                // Get user's special market settings if any
                string? specialMarketJson = user.SpecialMarket;

                // Apply special pricing if available
                var specialPricing = MarketPricingHelper.GetSpecialMarketPricing(coin.PairCode, specialMarketJson);
                if (specialPricing != null)
                {
                    (buyPrice, sellPrice) = MarketPricingHelper.ApplySpecialPricing(buyPrice, sellPrice, specialPricing);
                }

                // Calculate TRY amount to receive
                decimal tryAmountRaw = amount * sellPrice;
                decimal tryAmount = Math.Round(tryAmountRaw, 2); // TRY has 2 decimal places

                // Get user's coin balance
                var coinWallet = await _walletService.GetByUserIdAndCoinIdAsync(userId, coin.Id, _dbContext);
                decimal coinBalance = coinWallet?.Balance ?? 0;

                // Check if user has enough coin balance
                if (amount > coinBalance)
                {
                    response.Message = "Kullanılabilir bakiyenizi aştınız.";
                    return Ok(response);
                }

                // Check minimum and maximum sell limits
                if (amount < coin.MinimumSell)
                {
                    response.Message = $"<small>Minimum satılabilecek tutar: {coin.MinimumSell} {coin.Coin} ({coin.MinimumSell * sellPrice}$)</small>";
                    return Ok(response);
                }

                if (amount > coin.MaximumSell)
                {
                    response.Message = $"<small>Maksimum satılabilecek tutar: {coin.MaximumSell} {coin.Coin} ({coin.MaximumSell * sellPrice}$)</small>";
                    return Ok(response);
                }

                // Update user's balance
                decimal previousBalance = user.Balance;
                decimal newBalance = previousBalance + tryAmount;

                // Update user balance in database
                user.Balance = newBalance;
                user.ModDate = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync();

                // Update coin wallet
                decimal previousCoinBalance = coinBalance;
                decimal newCoinBalance = coinBalance - amount;

                if (coinWallet != null)
                {
                    coinWallet.Balance = newCoinBalance;
                    coinWallet.ModifiedDate = DateTime.UtcNow;
                    await _walletService.UpdateAsync(coinWallet,_dbContext);
                }
                else
                {
                    // This should not happen as we already checked the balance
                    response.Message = "Coin wallet not found";
                    return Ok(response);
                }

                // Create trade record
                var trade = new Trade
                {
                    Type = TradeType.Sell,
                    UserId = userId,
                    CoinId = coin.Id,
                    CoinRate = sellPrice,
                    CoinAmount = amount,
                    TryAmount = tryAmount,
                    PreviousCoinBalance = previousCoinBalance,
                    NewCoinBalance = newCoinBalance,
                    PreviousBalance = previousBalance,
                    NewBalance = newBalance,
                    PreviousWalletBalance = previousCoinBalance,
                    NewWalletBalance = coinWallet.Balance,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                var createdTrade = await _tradeService.CreateAsync(trade);

                await _balanceTransactionService.RecordTransactionAsync(
                   userId: userId,
                   transactionType: TransactionType.Sell,
                   amount: tryAmount,
                   description: _localizer["Sell: {0}", coin.Name].Value,
                   referenceId: createdTrade.Id,
                   referenceType: BalanceTransactionReferenceTypes.Trade,
                   existingContext: _dbContext
                );
                await transaction.CommitAsync();
                // Return success response
                response.Status = "success";
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selling coin {CoinCode}", request.Coin);
                response.Message = "An error occurred while processing your request.";
                return Ok(response);
            }
        }

        ///// <summary>
        ///// Generates sample data for a user with the specified email
        ///// Only works in debug mode and on localhost
        ///// </summary>
        ///// <param name="email">The email of the user to generate sample data for</param>
        ///// <returns>Success or error message</returns>
        //[HttpGet("generate-sample-data")]
        //public async Task<IActionResult> GenerateSampleDataAsync([FromQuery] string email)
        //{

        //    try
        //    {
        //        // Find the user by email
        //        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == email);
        //        if (user == null)
        //        {
        //            return NotFound($"User with email {email} not found");
        //        }

        //        // Generate sample data for the user
        //        await _sampleDataService.GenerateSampleDataForUserAsync(user.UserId, true);
        //        _logger.LogInformation("Generated sample data for user {UserId} with email {Email}", user.UserId, email);

        //        return Ok($"Sample data generated successfully for user {email}");
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error generating sample data for user with email {Email}", email);
        //        return StatusCode(500, $"Error generating sample data: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// Updates all market records to set MaximumBuy to decimal.MaxValue
        ///// Requires a security token (GUID) to prevent unauthorized access
        ///// </summary>
        ///// <param name="securityToken">A GUID security token to authorize the operation</param>
        ///// <returns>Success or error message with count of updated records</returns>
        //[HttpGet("update-max-buy-sell")]
        //public async Task<IActionResult> UpdateMaximumBuyAsync([FromQuery] string securityToken)
        //{
        //    try
        //    {
        //        // Validate security token against the specific expected value
        //        const string expectedToken = "31E516E1-8517-4CEB-B75A-045F4B0CB589";
        //        if (string.IsNullOrEmpty(securityToken) || !securityToken.Equals(expectedToken, StringComparison.OrdinalIgnoreCase))
        //        {
        //            return BadRequest("Invalid security token");
        //        }

        //        // Get all market records
        //        var markets = await _dbContext.Markets.ToListAsync();
        //        int updatedBuyCount = 0;
        //        int updatedSellCount = 0;

        //        foreach (var market in markets)
        //        {
        //            // Update MaximumBuy to decimal.MaxValue
        //            if (market.MaximumBuy != decimal.MaxValue)
        //            {
        //                market.MaximumBuy = decimal.MaxValue;
        //                updatedBuyCount++;
        //            }
        //            if (market.MaximumSell != decimal.MaxValue)
        //            {
        //                market.MaximumSell = decimal.MaxValue;
        //                updatedSellCount++;
        //            }
        //        }

        //        // Save changes if any records were updated
        //        if (updatedBuyCount > 0 || updatedSellCount > 0)
        //        {
        //            await _dbContext.SaveChangesAsync();
        //            _logger.LogInformation("Updated MaximumBuy to decimal.MaxValue for {Count} market records", updatedBuyCount);
        //            _logger.LogInformation("Updated MaximumSell to decimal.MaxValue for {Count} market records", updatedSellCount);
        //        }

        //        return Ok($"Successfully updated MaximumBuy and MaximumSell to decimal.MaxValue for {updatedBuyCount + updatedSellCount} market records");
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error updating MaximumBuy values");
        //        return StatusCode(500, $"Error updating MaximumBuy values: {ex.Message}");
        //    }
        //}
    }
}