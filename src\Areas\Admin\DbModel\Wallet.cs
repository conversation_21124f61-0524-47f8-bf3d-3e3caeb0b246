using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("WALLET")]
    public class Wallet
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("COIN_ID")]
        public int CoinId { get; set; }

        [Required]
        [Column("BALANCE", TypeName = "decimal(20,8)")]
        public decimal Balance { get; set; }

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; } = DateTime.UtcNow;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CoinId")]
        public virtual Market Coin { get; set; } = null!;
    }
}
