using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

public interface ITradeService
{
    Task<Trade?> GetByIdAsync(int id);
    Task<List<TradeViewModel>> GetListAsync();
    Task<List<TradeViewModel>> GetByUserIdAsync(int userId);
    Task DeleteAsync(int id);
    Task<Trade> CreateAsync(Trade trade);
    Task UpdateAsync(Trade trade);
}
