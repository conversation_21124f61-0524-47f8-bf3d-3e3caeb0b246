@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Trades"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Trades"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Trades"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title float-right">@L["Count"]: @(Model.Trades.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["User"]</th>
                        <th>@L["Coin"]</th>
                        <th>@L["Type"]</th>
                        <th>@L["Amount"]</th>
                        <th>@L["Rate"]</th>
                        <th>@L["Total"]</th>
                        <th>@L["Description"]</th>
                        <th>@L["Date"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Trades)
                    {
                        <tr>
                            <td>@item.UserEmail</td>
                            <td>@item.CoinCode</td>
                            <td>
                                @if (item.Type == TradeType.Buy)
                                {
                                    <span class="badge badge-success">@L["Buy"]</span>
                                }
                                else if (item.Type == TradeType.Sell)
                                {
                                    <span class="badge badge-danger">@L["Sell"]</span>
                                }
                                else if (item.Type == TradeType.PackageBonus)
                                {
                                    <span class="badge badge-info">@L["Package Bonus"]</span>
                                }
                                else if (item.Type == TradeType.ReferralReward)
                                {
                                    <span class="badge badge-warning">@L["Referral Reward"]</span>
                                }
                            </td>
                            <td>@item.CoinAmount.ToString("N8")</td>
                            <td>@item.CoinRate.ToString("N2") @L["Currency_Symbol"]</td>
                            <td>@item.TryAmount.ToString("N2") @L["Currency_Symbol"]</td>
                            <td>
                                @if (item.ReferralReward != null)
                                {
                                    <div class="text-muted small">
                                        @item.ReferralReward.GetReferralRewardDescription()
                                    </div>
                                }
                            </td>
                            <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                            <td>
                                <!-- Edit and Delete buttons hidden -->
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

@section Styles {
    <style>
        .table td {
            vertical-align: middle;
        }
        .text-muted.small {
            white-space: normal;
            line-height: 1.4;
        }
    </style>
}
