using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class DepositServiceTests : TestBase
    {
        private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
        private readonly Mock<IBalanceTransactionService> _mockBalanceTransactionService;
        private readonly Mock<ILogger<DepositService>> _mockLogger;

        public DepositServiceTests()
        {
            _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            _mockBalanceTransactionService = new Mock<IBalanceTransactionService>();
            _mockLogger = new Mock<ILogger<DepositService>>();

            // Setup localizer
            _mockLocalizer.Setup(l => l[It.IsAny<string>()])
                .Returns<string>(s => new LocalizedString(s, s));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns<string, object[]>((s, args) => new LocalizedString(s, string.Format(s, args)));
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ReturnsDeposit()
        {
            // Arrange
            var dbContext = CreateDbContext("GetByIdAsync_WithValidId_ReturnsDeposit");
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);

            var deposit = new Deposit
            {
                Id = 1,
                UserId = user.UserId,
                Amount = 1000m,
                DepositType = "Bank Transfer",
                FullName = "Test User",
                Status = DepositStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.GetByIdAsync(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(1000m, result.Amount);
            Assert.Equal("Bank Transfer", result.DepositType);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var dbContext = CreateDbContext("GetByIdAsync_WithInvalidId_ReturnsNull");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.GetByIdAsync(999);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetListAsync_ReturnsAllDeposits()
        {
            // Arrange
            var dbContext = CreateDbContext("GetListAsync_ReturnsAllDeposits");
            var user1 = TestDataGenerator.CreateUser(1);
            var user2 = TestDataGenerator.CreateUser(2);
            dbContext.Users.AddRange(user1, user2);

            var deposits = new List<Deposit>
            {
                new Deposit
                {
                    Id = 1,
                    UserId = user1.UserId,
                    Amount = 1000m,
                    DepositType = "Bank Transfer",
                    FullName = "User One",
                    Status = DepositStatus.Approved,
                    CreatedDate = DateTime.UtcNow.AddDays(-1)
                },
                new Deposit
                {
                    Id = 2,
                    UserId = user2.UserId,
                    Amount = 500m,
                    DepositType = "Crypto - BTC",
                    FullName = "User Two",
                    Status = DepositStatus.Pending,
                    CreatedDate = DateTime.UtcNow
                }
            };
            dbContext.Deposits.AddRange(deposits);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.GetListAsync();

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Contains(result, d => d.Id == 1 && d.Amount == 1000m);
            Assert.Contains(result, d => d.Id == 2 && d.Amount == 500m);
        }

        [Fact]
        public async Task GetCryptoDepositsAsync_ReturnsOnlyCryptoDeposits()
        {
            // Arrange
            var dbContext = CreateDbContext("GetCryptoDepositsAsync_ReturnsOnlyCryptoDeposits");
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);

            var deposits = new List<Deposit>
            {
                new Deposit
                {
                    Id = 1,
                    UserId = user.UserId,
                    Amount = 1000m,
                    DepositType = "Bank Transfer",
                    FullName = "User One",
                    Status = DepositStatus.Approved,
                    CreatedDate = DateTime.UtcNow
                },
                new Deposit
                {
                    Id = 2,
                    UserId = user.UserId,
                    Amount = 500m,
                    DepositType = "Crypto - BTC",
                    FullName = "User One",
                    Status = DepositStatus.Pending,
                    CreatedDate = DateTime.UtcNow
                }
            };
            dbContext.Deposits.AddRange(deposits);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.GetCryptoDepositsAsync();

            // Assert
            Assert.Single(result);
            Assert.Equal(2, result[0].Id);
            Assert.Equal("Crypto - BTC", result[0].DepositType);
            Assert.True(result[0].IsCryptoDeposit);
        }

        /// <summary>
        /// Tests that when a deposit is approved, the user balance is updated correctly
        /// and RecordTransactionAsync is called with the correct parameters.
        /// </summary>
        [Fact]
        public async Task UpdateStatusAsync_ToApproved_UpdatesBalanceAndCallsRecordTransactionAsync()
        {
            // Arrange
            var dbContext = CreateDbContext("UpdateStatusAsync_ToApproved_UpdatesBalanceAndCallsRecordTransactionAsync");
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 500m;
            dbContext.Users.Add(user);

            var deposit = new Deposit
            {
                Id = 1,
                UserId = user.UserId,
                Amount = 200m,
                Status = DepositStatus.Pending,
                DepositType = "Bank Transfer",
                FullName = "Test User",
                CreatedDate = DateTime.UtcNow,
                RewardStatus = DepositRewardStatus.NotEligible
            };
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Setup mock to return a balance transaction
            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(new BalanceTransaction { Id = 1 });

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.UpdateStatusAsync(deposit.Id, DepositStatus.Approved);

            // Assert
            Assert.True(result);

            // Verify deposit status was updated
            var updatedDeposit = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.Equal(DepositStatus.Approved, updatedDeposit.Status);

            // Verify user balance was updated
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(700m, updatedUser.Balance); // 500 + 200 = 700

            // Verify RecordTransactionAsync was called with correct parameters
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                user.UserId, // userId
                TransactionType.Deposit, // transactionType
                200m, // amount
                500m, // previousBalance
                700m, // newBalance
                It.IsAny<string>(), // description
                deposit.Id, // referenceId
                BalanceTransactionReferenceTypes.Deposit, // referenceType
                dbContext // existingContext
            ), Times.Once);
        }

        /// <summary>
        /// Tests that when a deposit is reversed (approved to pending),
        /// the user balance is correctly reduced.
        /// </summary>
        [Fact]
        public async Task UpdateStatusAsync_FromApprovedToPending_ReducesBalance()
        {
            // Arrange
            var dbContext = CreateDbContext("UpdateStatusAsync_FromApprovedToPending_ReducesBalance");
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 700m; // Already includes the 200m deposit
            dbContext.Users.Add(user);

            var deposit = new Deposit
            {
                Id = 1,
                UserId = user.UserId,
                Amount = 200m,
                Status = DepositStatus.Approved,
                DepositType = "Bank Transfer",
                FullName = "Test User",
                CreatedDate = DateTime.UtcNow,
                RewardStatus = DepositRewardStatus.NotEligible
            };
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Setup mock to return a balance transaction
            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(new BalanceTransaction { Id = 1 });

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.UpdateStatusAsync(deposit.Id, DepositStatus.Pending);

            // Assert
            Assert.True(result);

            // Verify deposit status was updated
            var updatedDeposit = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.Equal(DepositStatus.Pending, updatedDeposit.Status);

            // Verify user balance was reduced
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(500m, updatedUser.Balance); // 700 - 200 = 500

            // Verify RecordTransactionAsync was called for reversal
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                user.UserId, // userId
                TransactionType.Withdrawal, // transactionType (reversal)
                200m, // amount
                700m, // previousBalance
                500m, // newBalance
                It.IsAny<string>(), // description
                deposit.Id, // referenceId
                BalanceTransactionReferenceTypes.Deposit, // referenceType
                dbContext // existingContext
            ), Times.Once);
        }

        /// <summary>
        /// Tests that when a deposit reversal would result in negative balance,
        /// the balance is set to zero instead.
        /// </summary>
        [Fact]
        public async Task UpdateStatusAsync_FromApprovedToPending_WithInsufficientBalance_SetsBalanceToZero()
        {
            // Arrange
            var dbContext = CreateDbContext("UpdateStatusAsync_FromApprovedToPending_WithInsufficientBalance_SetsBalanceToZero");
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 50m; // Less than the deposit amount
            dbContext.Users.Add(user);

            var deposit = new Deposit
            {
                Id = 1,
                UserId = user.UserId,
                Amount = 200m,
                Status = DepositStatus.Approved,
                DepositType = "Bank Transfer",
                FullName = "Test User",
                CreatedDate = DateTime.UtcNow,
                RewardStatus = DepositRewardStatus.NotEligible
            };
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Setup mock to return a balance transaction
            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(new BalanceTransaction { Id = 1 });

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);

            var depositService = new DepositService(
                _mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                _mockBalanceTransactionService.Object,
                mockTokenPriceService.Object,
                _mockLogger.Object
            );

            // Act
            var result = await depositService.UpdateStatusAsync(deposit.Id, DepositStatus.Pending);

            // Assert
            Assert.True(result);

            // Verify deposit status was updated
            var updatedDeposit = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.Equal(DepositStatus.Pending, updatedDeposit.Status);

            // Verify user balance was set to zero (not negative)
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(0m, updatedUser.Balance);

            // Verify RecordTransactionAsync was called with zero as new balance
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                user.UserId, // userId
                TransactionType.Withdrawal, // transactionType (reversal)
                200m, // amount
                50m, // previousBalance
                0m, // newBalance (set to zero)
                It.IsAny<string>(), // description
                deposit.Id, // referenceId
                BalanceTransactionReferenceTypes.Deposit, // referenceType
                dbContext // existingContext
            ), Times.Once);
        }
    }
}
