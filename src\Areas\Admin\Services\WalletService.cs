using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Areas.Admin.Services;

public class WalletService : IWalletService
{
    private readonly AppDbContext _context;

    public WalletService(AppDbContext context)
    {
        _context = context;
    }

    public async Task<Wallet?> GetByIdAsync(int id)
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .ToListAsync();
    }

    public async Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => (double)w.Balance) // Cast decimal to double for SQLite
            .Take(topN)
            .ToListAsync();
    }

    public async Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        return await contextToUse.Wallets
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == coinId);
    }

    public async Task<List<WalletViewModel>> GetListAsync()
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin != null ? w.Coin.PairCode : string.Empty,
                Balance = w.Balance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }



    public async Task<Wallet> CreateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        if (existingContext == null)
        {
            _context.Wallets.Add(wallet);
            await _context.SaveChangesAsync();
            return wallet;
        }
        else
        {
            existingContext.Wallets.Add(wallet);
            await existingContext.SaveChangesAsync();
            return wallet;
        }
    }

    public async Task UpdateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        wallet.ModifiedDate = DateTime.UtcNow;
        contextToUse.Entry(wallet).State = EntityState.Modified;
        await contextToUse.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var wallet = await _context.Wallets.FindAsync(id);
        if (wallet != null)
        {
            _context.Wallets.Remove(wallet);
            await _context.SaveChangesAsync();
        }
    }

    //public async Task<Wallet> AddBalanceAsync(int userId, int coinId, decimal amount)
    //{
    //    var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);

    //    if (wallet == null)
    //    {
    //        // Create new wallet if it doesn't exist
    //        wallet = new Wallet
    //        {
    //            UserId = userId,
    //            CoinId = coinId,
    //            Balance = amount,
    //            CreatedDate = DateTime.UtcNow
    //        };
    //        return await CreateAsync(wallet);
    //    }
    //    else
    //    {
    //        // Update existing wallet
    //        wallet.Balance += amount;
    //        wallet.ModifiedDate = DateTime.UtcNow;
    //        await UpdateAsync(wallet);
    //        return wallet;
    //    }
    //}
    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);

        if (wallet == null)
        {
            // Create new wallet if it doesn't exist
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                CreatedDate = DateTime.UtcNow
            };
            return await CreateAsync(wallet, contextToUse);
        }
        else
        {
            // Update existing wallet
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet, contextToUse);
            return wallet;
        }


    }
    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, existingContext);

        if (wallet == null || wallet.Balance < amount)
        {
            return false; // Insufficient balance
        }

        wallet.Balance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet, existingContext);
        return true;
    }

    // NEW METHODS - Available Balance
    public async Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.Balance ?? 0;
    }
}
