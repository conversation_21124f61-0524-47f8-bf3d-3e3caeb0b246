using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.Data;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class BalanceTransactionServiceTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
        private readonly BalanceTransactionService _service;

        public BalanceTransactionServiceTests()
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new AppDbContext(options);
            _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            _service = new BalanceTransactionService(_context, _mockLocalizer.Object);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldCreateBalanceTransactionRecord_WithoutUpdatingUserBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                Balance = 1000m,
                IsActive = 1
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            decimal previousBalance = 1000m;
            decimal newBalance = 900m;
            decimal amount = 100m;

            // Act
            var result = await _service.RecordTransactionAsync(
                userId: user.UserId,
                transactionType: TransactionType.Buy,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance,
                description: "Test transaction",
                referenceId: 123,
                referenceType: "TestType"
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(TransactionType.Buy, result.TransactionType);
            Assert.Equal(amount, result.Amount);
            Assert.Equal(previousBalance, result.PreviousBalance);
            Assert.Equal(newBalance, result.NewBalance);
            Assert.Equal("Test transaction", result.Description);
            Assert.Equal(123, result.ReferenceId);
            Assert.Equal("TestType", result.ReferenceType);
            Assert.True(result.IsActive);

            // Verify that user balance was NOT updated by RecordTransactionAsync
            var userAfter = await _context.Users.FindAsync(user.UserId);
            Assert.Equal(1000m, userAfter.Balance); // Should remain unchanged
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldCreateRecord_WithExistingContext()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                Balance = 500m,
                IsActive = 1
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.RecordTransactionAsync(
                userId: user.UserId,
                transactionType: TransactionType.Deposit,
                amount: 200m,
                previousBalance: 500m,
                newBalance: 700m,
                description: "Deposit transaction",
                existingContext: _context
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(TransactionType.Deposit, result.TransactionType);
            Assert.Equal(200m, result.Amount);
            Assert.Equal(500m, result.PreviousBalance);
            Assert.Equal(700m, result.NewBalance);

            // Verify record exists in database
            var savedRecord = await _context.BalanceTransactions.FindAsync(result.Id);
            Assert.NotNull(savedRecord);
            Assert.Equal(result.Id, savedRecord.Id);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldHandleNullOptionalParameters()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                Balance = 300m,
                IsActive = 1
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.RecordTransactionAsync(
                userId: user.UserId,
                transactionType: TransactionType.Sell,
                amount: 50m,
                previousBalance: 300m,
                newBalance: 350m
                // description, referenceId, referenceType, existingContext are null/default
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(TransactionType.Sell, result.TransactionType);
            Assert.Equal(50m, result.Amount);
            Assert.Null(result.Description);
            Assert.Null(result.ReferenceId);
            Assert.Null(result.ReferenceType);
        }

        [Theory]
        [InlineData(TransactionType.Buy)]
        [InlineData(TransactionType.Sell)]
        [InlineData(TransactionType.Deposit)]
        [InlineData(TransactionType.Withdrawal)]
        [InlineData(TransactionType.PackagePurchase)]
        [InlineData(TransactionType.ReferralReward)]
        public async Task RecordTransactionAsync_ShouldHandleAllTransactionTypes(string transactionType)
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                Balance = 1000m,
                IsActive = 1
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.RecordTransactionAsync(
                userId: user.UserId,
                transactionType: transactionType,
                amount: 100m,
                previousBalance: 1000m,
                newBalance: 900m,
                description: $"Test {transactionType}"
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(transactionType, result.TransactionType);
            Assert.Equal($"Test {transactionType}", result.Description);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldSetCreatedDateToUtcNow()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                Balance = 1000m,
                IsActive = 1
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            var beforeCall = DateTime.UtcNow;

            // Act
            var result = await _service.RecordTransactionAsync(
                userId: user.UserId,
                transactionType: TransactionType.Buy,
                amount: 100m,
                previousBalance: 1000m,
                newBalance: 900m
            );

            var afterCall = DateTime.UtcNow;

            // Assert
            Assert.True(result.CreatedDate >= beforeCall);
            Assert.True(result.CreatedDate <= afterCall);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
