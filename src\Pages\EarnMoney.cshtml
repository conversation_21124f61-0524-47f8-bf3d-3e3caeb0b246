@page
@model RazeWinComTr.Pages.EarnMoneyModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Earn Money with Crypto"];
}

<div class="fw earnMoneySection">
    <div class="container">
        <div class="fw earnMoneyHeader">
            <h1>@Localizer["Earn Money with Cryptocurrency"]</h1>
            <p>@Localizer["Multiple ways to grow your digital assets with RazeWin"]</p>
        </div>

        <div class="row earnMoneyOptions">
            <div class="col-md-4">
                <div class="earnCard">
                    <div class="earnCardIcon">
                        <i class="flaticon-graph"></i>
                    </div>
                    <h3>@Localizer["Staking"]</h3>
                    <p>@Localizer["Earn passive income by holding and staking your RZW tokens."]</p>
                    <div class="earnRates">
                        <div class="earnRate">
                            <span class="coinName">RazeWin (RZW)</span>
                            <span class="rateValue">8.5% APY</span>
                        </div>
                    </div>
                    <a href="#" class="earnCardButton">@Localizer["Start Staking"]</a>
                </div>
            </div>

            <div class="col-md-4">
                <div class="earnCard">
                    <div class="earnCardIcon">
                        <i class="flaticon-transfer"></i>
                    </div>
                    <h3>@Localizer["Savings"]</h3>
                    <p>@Localizer["Deposit your RZW tokens and earn interest with flexible terms."]</p>
                    <div class="earnRates">
                        <div class="earnRate">
                            <span class="coinName">RazeWin (RZW)</span>
                            <span class="rateValue">10.2% APY</span>
                        </div>
                    </div>
                    <a href="#" class="earnCardButton">@Localizer["Start Saving"]</a>
                </div>
            </div>

            <div class="col-md-4">
                <div class="earnCard">
                    <div class="earnCardIcon">
                        <i class="flaticon-arrow"></i>
                    </div>
                    <h3>@Localizer["Lending"]</h3>
                    <p>@Localizer["Lend your RZW tokens to earn interest while helping others."]</p>
                    <div class="earnRates">
                        <div class="earnRate">
                            <span class="coinName">RazeWin (RZW)</span>
                            <span class="rateValue">7.5% APY</span>
                        </div>
                    </div>
                    <a href="#" class="earnCardButton">@Localizer["Start Lending"]</a>
                </div>
            </div>
        </div>

        <div class="earnInfoSection">
            <div class="row">
                <div class="col-md-6">
                    <div class="earnInfoContent">
                        <h2>@Localizer["Why Earn with RazeWin?"]</h2>
                        <ul class="earnBenefits">
                            <li><i class="flaticon-check"></i> @Localizer["Competitive interest rates"]</li>
                            <li><i class="flaticon-check"></i> @Localizer["Secure platform with insurance"]</li>
                            <li><i class="flaticon-check"></i> @Localizer["Flexible terms and withdrawal options"]</li>
                            <li><i class="flaticon-check"></i> @Localizer["24/7 customer support"]</li>
                            <li><i class="flaticon-check"></i> @Localizer["No hidden fees"]</li>
                        </ul>
                        <a href="/register" class="earnInfoButton">@Localizer["Create Account to Start Earning"]</a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="earnInfoImage">
                        <img src="/images/earn-money2.png" alt="Earn with RazeWin" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .earnMoneySection {
        padding: 60px 0;
        background-color: #191919;
        color: #fff;
    }

    .earnMoneyHeader {
        text-align: center;
        margin-bottom: 50px;
    }

    .earnMoneyHeader h1 {
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
    }

    .earnMoneyHeader p {
        font-size: 18px;
        color: #ccc;
    }

    .earnMoneyOptions {
        margin-bottom: 60px;
    }

    .earnCard {
        background-color: #252525;
        border-radius: 10px;
        padding: 30px;
        height: 100%;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .earnCard:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    }

    .earnCardIcon {
        font-size: 40px;
        color: #f7931a;
        margin-bottom: 20px;
    }

    .earnCard h3 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #fff;
    }

    .earnCard p {
        font-size: 16px;
        color: #ccc;
        margin-bottom: 20px;
    }

    .earnRates {
        background-color: #1e1e1e;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .earnRate {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #333;
    }

    .earnRate:last-child {
        border-bottom: none;
    }

    .coinName {
        color: #ddd;
    }

    .rateValue {
        color: #f7931a;
        font-weight: 600;
    }

    .earnCardButton {
        display: inline-block;
        background-color: #f7931a;
        color: #fff;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
    }

    .earnCardButton:hover {
        background-color: #e67e00;
    }

    .earnInfoSection {
        background-color: #252525;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .earnInfoContent h2 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 25px;
        color: #fff;
    }

    .earnBenefits {
        list-style: none;
        padding: 0;
        margin-bottom: 30px;
    }

    .earnBenefits li {
        padding: 10px 0;
        font-size: 16px;
        color: #ddd;
    }

    .earnBenefits li i {
        color: #f7931a;
        margin-right: 10px;
    }

    .earnInfoButton {
        display: inline-block;
        background-color: #f7931a;
        color: #fff;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .earnInfoButton:hover {
        background-color: #e67e00;
    }

    .earnInfoImage {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .earnInfoImage img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
    }

    @@media (max-width: 768px) {
        .earnMoneyOptions .col-md-4 {
            margin-bottom: 30px;
        }

        .earnInfoImage {
            margin-top: 30px;
        }
    }
</style>
